#!/usr/bin/env python3
"""
过拟合分析 - 检查训练数据集范围和测试数据重叠问题
"""

import os
import sys
import random
import numpy as np
from pathlib import Path
import logging
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from model_validator import validate_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_training_data_range():
    """分析训练数据集的范围"""
    
    # 检查快速训练使用的数据集范围
    dataset_path = Path('../dataset/cat_individual_images')
    
    # 获取所有猫咪ID
    all_cat_ids = []
    for cat_folder in dataset_path.iterdir():
        if cat_folder.is_dir() and cat_folder.name.isdigit():
            all_cat_ids.append(int(cat_folder.name))
    
    all_cat_ids.sort()
    
    print("🔍 训练数据集范围分析")
    print("=" * 60)
    print(f"数据集中猫咪ID范围: {min(all_cat_ids):04d} - {max(all_cat_ids):04d}")
    print(f"总猫咪数量: {len(all_cat_ids)}")
    
    # 分析快速训练使用的100只猫咪
    # 根据代码，选择的是图片数量最多的前100只
    cat_image_counts = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            cat_image_counts.append((cat_folder.name, len(images)))
    
    # 按图片数量排序
    cat_image_counts.sort(key=lambda x: x[1], reverse=True)
    
    # 前100只用于训练
    training_cats = [cat_id for cat_id, _ in cat_image_counts[:100]]
    training_cat_ids = [int(cat_id) for cat_id in training_cats]
    training_cat_ids.sort()
    
    print(f"\n📊 快速训练使用的100只猫咪:")
    print(f"ID范围: {min(training_cat_ids):04d} - {max(training_cat_ids):04d}")
    print(f"训练猫咪ID: {training_cats[:10]}... (显示前10只)")
    
    # 检查0008和0413是否在训练集中
    cat_0008_in_training = '0008' in training_cats
    cat_0413_in_training = '0413' in training_cats
    
    print(f"\n🎯 关键检查:")
    print(f"猫咪0008是否在训练集: {'是' if cat_0008_in_training else '否'}")
    print(f"猫咪0413是否在训练集: {'是' if cat_0413_in_training else '否'}")
    
    return {
        'all_cats': len(all_cat_ids),
        'training_cats': training_cats,
        'cat_0008_in_training': cat_0008_in_training,
        'cat_0413_in_training': cat_0413_in_training
    }

def test_specific_cats(model_path: str):
    """测试特定的猫咪对比"""
    
    print("\n🔬 特定猫咪对比测试")
    print("=" * 60)
    
    # 测试0008和0413
    test_cases = [
        ('0008', '../dataset/cat_individual_images/0008/0008_000.JPG'),
        ('0413', '../dataset/cat_individual_images/0413/0413_000.jpg')
    ]
    
    from model_validator import FlexibleCatRecognizer
    
    # 创建识别器
    recognizer = FlexibleCatRecognizer(model_path, device='cpu', strategy='balanced')
    
    # 注册两只猫咪
    for cat_id, first_image in test_cases:
        cat_folder = Path(f'../dataset/cat_individual_images/{cat_id}')
        if cat_folder.exists():
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend([str(img) for img in cat_folder.glob(ext)])
            
            if len(images) >= 5:
                train_images = images[:max(3, int(len(images) * 0.7))]
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                print(f"注册猫咪{cat_id}: {'成功' if result['success'] else '失败'}")
    
    # 测试识别
    print(f"\n🧪 交叉识别测试:")
    for cat_id, test_image in test_cases:
        if Path(test_image).exists():
            result = recognizer.recognize_cat(test_image)
            predicted = result.get('cat_id', 'unknown')
            similarity = result.get('similarity', 0.0)
            confidence = result.get('confidence', 0.0)
            
            status = "✅" if predicted == cat_id else "❌"
            print(f"{status} 真实:{cat_id} 预测:{predicted} 相似度:{similarity:.3f} 置信度:{confidence:.1%}")
        else:
            print(f"❌ 图片不存在: {test_image}")

def test_unseen_cats(model_path: str, num_tests: int = 20):
    """测试未见过的猫咪（不在训练集中的）"""
    
    print(f"\n🔍 未见过猫咪测试 (测试{num_tests}只)")
    print("=" * 60)
    
    # 获取训练数据分析
    analysis = analyze_training_data_range()
    training_cats = set(analysis['training_cats'])
    
    # 找到未在训练集中的猫咪
    dataset_path = Path('../dataset/cat_individual_images')
    unseen_cats = []
    
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        cat_id = cat_folder.name
        if cat_id not in training_cats:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                unseen_cats.append((cat_id, [str(img) for img in images]))
    
    print(f"找到 {len(unseen_cats)} 只未见过的猫咪")
    
    if len(unseen_cats) < num_tests:
        print(f"⚠️ 可用未见过猫咪数量不足，实际测试 {len(unseen_cats)} 只")
        num_tests = len(unseen_cats)
    
    # 随机选择测试猫咪
    test_unseen_cats = random.sample(unseen_cats, num_tests)
    
    from model_validator import FlexibleCatRecognizer
    
    # 创建识别器
    recognizer = FlexibleCatRecognizer(model_path, device='cpu', strategy='balanced')
    
    # 注册这些未见过的猫咪
    registered_cats = []
    for cat_id, image_paths in test_unseen_cats:
        train_count = max(3, int(len(image_paths) * 0.7))
        train_images = image_paths[:train_count]
        test_images = image_paths[train_count:]
        
        result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
        if result['success'] and test_images:
            registered_cats.append((cat_id, test_images))
    
    print(f"成功注册 {len(registered_cats)} 只未见过的猫咪")
    
    # 识别测试
    correct = 0
    total = 0
    confidences = []
    similarities = []
    
    for cat_id, test_images in registered_cats:
        test_image = random.choice(test_images)
        result = recognizer.recognize_cat(test_image)
        
        total += 1
        is_correct = result.get('success') and result.get('cat_id') == cat_id
        predicted = result.get('cat_id', 'unknown')
        similarity = result.get('similarity', 0.0)
        confidence = result.get('confidence', 0.0)
        
        if is_correct:
            correct += 1
            confidences.append(confidence)
        
        similarities.append(similarity)
        
        # 显示结果
        status = "✅" if is_correct else "❌"
        print(f"{status} 真实:{cat_id} 预测:{predicted} 相似度:{similarity:.3f} 置信度:{confidence:.1%}")
    
    accuracy = correct / total if total > 0 else 0.0
    avg_confidence = np.mean(confidences) if confidences else 0.0
    avg_similarity = np.mean(similarities) if similarities else 0.0
    
    print(f"\n📊 未见过猫咪测试结果:")
    print(f"   准确率: {accuracy:.1%} ({correct}/{total})")
    print(f"   平均置信度: {avg_confidence:.1%}")
    print(f"   平均相似度: {avg_similarity:.3f}")
    
    return {
        'accuracy': accuracy,
        'correct': correct,
        'total': total,
        'avg_confidence': avg_confidence,
        'avg_similarity': avg_similarity,
        'unseen_cats_tested': [cat_id for cat_id, _ in registered_cats]
    }

def analyze_confidence_distribution(model_path: str):
    """分析置信度分布问题"""
    
    print(f"\n📈 置信度分布分析")
    print("=" * 60)
    
    # 测试多种情况的置信度
    from model_validator import FlexibleCatRecognizer
    
    recognizer = FlexibleCatRecognizer(model_path, device='cpu', strategy='balanced')
    
    # 随机选择几只猫咪进行测试
    dataset_path = Path('../dataset/cat_individual_images')
    available_cats = []
    
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    # 选择5只猫咪
    test_cats = random.sample(available_cats, 5)
    
    # 注册猫咪
    for cat_id, image_paths in test_cats:
        train_count = max(3, int(len(image_paths) * 0.7))
        train_images = image_paths[:train_count]
        recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
    
    # 收集置信度数据
    correct_confidences = []
    incorrect_confidences = []
    correct_similarities = []
    incorrect_similarities = []
    
    # 测试正确识别的置信度
    for cat_id, image_paths in test_cats:
        test_images = image_paths[max(3, int(len(image_paths) * 0.7)):]
        for test_image in test_images[:3]:  # 每只猫测试3张
            result = recognizer.recognize_cat(test_image)
            
            if result.get('success') and result.get('cat_id') == cat_id:
                correct_confidences.append(result.get('confidence', 0.0))
                correct_similarities.append(result.get('similarity', 0.0))
    
    # 测试错误识别的置信度（交叉测试）
    for i, (cat_id, image_paths) in enumerate(test_cats):
        test_images = image_paths[max(3, int(len(image_paths) * 0.7)):]
        for j, (other_cat_id, _) in enumerate(test_cats):
            if i != j:  # 用其他猫的图片测试
                for test_image in test_images[:2]:  # 每对测试2张
                    result = recognizer.recognize_cat(test_image)
                    
                    if result.get('success') and result.get('cat_id') == other_cat_id:
                        incorrect_confidences.append(result.get('confidence', 0.0))
                        incorrect_similarities.append(result.get('similarity', 0.0))
    
    print(f"正确识别样本数: {len(correct_confidences)}")
    print(f"错误识别样本数: {len(incorrect_confidences)}")
    
    if correct_confidences:
        print(f"\n✅ 正确识别:")
        print(f"   平均置信度: {np.mean(correct_confidences):.1%}")
        print(f"   置信度范围: {np.min(correct_confidences):.1%} - {np.max(correct_confidences):.1%}")
        print(f"   平均相似度: {np.mean(correct_similarities):.3f}")
    
    if incorrect_confidences:
        print(f"\n❌ 错误识别:")
        print(f"   平均置信度: {np.mean(incorrect_confidences):.1%}")
        print(f"   置信度范围: {np.min(incorrect_confidences):.1%} - {np.max(incorrect_confidences):.1%}")
        print(f"   平均相似度: {np.mean(incorrect_similarities):.3f}")
    
    # 分析置信度重叠问题
    if correct_confidences and incorrect_confidences:
        overlap_threshold = 0.9
        high_conf_correct = sum(1 for c in correct_confidences if c >= overlap_threshold)
        high_conf_incorrect = sum(1 for c in incorrect_confidences if c >= overlap_threshold)
        
        print(f"\n⚠️ 高置信度重叠分析 (>={overlap_threshold:.0%}):")
        print(f"   正确识别中高置信度: {high_conf_correct}/{len(correct_confidences)} ({high_conf_correct/len(correct_confidences):.1%})")
        print(f"   错误识别中高置信度: {high_conf_incorrect}/{len(incorrect_confidences)} ({high_conf_incorrect/len(incorrect_confidences):.1%})")
        
        if high_conf_incorrect > 0:
            print(f"   🚨 问题: 错误识别也有高置信度，说明模型过于自信")

def main():
    """主函数"""
    model_path = 'fast_megadescriptor_100cats_best.pth'
    
    print("🔍 过拟合和置信度问题分析")
    print("=" * 80)
    
    # 1. 分析训练数据范围
    training_analysis = analyze_training_data_range()
    
    # 2. 测试特定猫咪对比
    test_specific_cats(model_path)
    
    # 3. 测试未见过的猫咪
    unseen_results = test_unseen_cats(model_path, num_tests=10)
    
    # 4. 分析置信度分布
    analyze_confidence_distribution(model_path)
    
    # 5. 总结分析
    print(f"\n🎯 总结分析:")
    print("=" * 60)
    
    if training_analysis['cat_0008_in_training'] or training_analysis['cat_0413_in_training']:
        print("⚠️ 可能存在数据泄露: 测试的猫咪在训练集中")
    
    if unseen_results['accuracy'] < 0.8:
        print("⚠️ 泛化能力不足: 未见过猫咪的准确率较低")
    
    print(f"📊 未见过猫咪准确率: {unseen_results['accuracy']:.1%}")
    print(f"🎯 建议: 需要使用完全独立的测试集来验证真实性能")

if __name__ == "__main__":
    main()
