#!/usr/bin/env python3
"""
使用wildlife-tools进行专业的猫咪重识别训练
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
import itertools
from typing import Dict, List, Tuple

import torch
import torch.optim as optim
import torchvision.transforms as T
import timm

# wildlife-tools imports
from wildlife_tools.data import ImageDataset
from wildlife_tools.train import BasicTrainer, ArcFaceLoss, TripletLoss, set_seed
from wildlife_tools.features import DeepFeatures
from wildlife_tools.similarity import CosineSimilarity
from wildlife_tools.inference import KnnClassifier

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CatDatasetCreator:
    """创建符合wildlife-tools格式的猫咪数据集"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        self.split_file = split_file
        
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            self.split_info = json.load(f)
        
        logger.info(f"数据集分割信息加载完成")
        logger.info(f"训练集: {self.split_info['train_cats']} 只猫咪")
        logger.info(f"测试集: {self.split_info['test_cats']} 只猫咪")
    
    def create_dataframe(self, use_train_set: bool = True) -> pd.DataFrame:
        """创建符合wildlife-tools格式的DataFrame"""
        
        cats_data = self.split_info['train_data'] if use_train_set else self.split_info['test_data']
        
        # 创建DataFrame
        rows = []
        for cat in cats_data:
            cat_id = cat['cat_id']
            images = cat['images']
            
            for img_path in images:
                # 转换为相对路径
                relative_path = os.path.relpath(img_path, '../dataset/cat_individual_images')
                
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        df = pd.DataFrame(rows)
        
        dataset_type = "训练集" if use_train_set else "测试集"
        logger.info(f"{dataset_type} DataFrame创建完成:")
        logger.info(f"  总图片数: {len(df)}")
        logger.info(f"  猫咪数量: {df['identity'].nunique()}")
        logger.info(f"  平均图片/猫: {len(df) / df['identity'].nunique():.1f}")
        
        return df
    
    def create_image_dataset(self, use_train_set: bool = True) -> ImageDataset:
        """创建ImageDataset"""
        
        df = self.create_dataframe(use_train_set)
        root_path = '../dataset/cat_individual_images'
        
        # 数据增强
        if use_train_set:
            # 训练时使用数据增强
            transform = T.Compose([
                T.Resize([256, 256]),
                T.RandomCrop([224, 224]),
                T.RandomHorizontalFlip(p=0.5),
                T.RandomRotation(degrees=15),
                T.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            # 测试时不使用数据增强
            transform = T.Compose([
                T.Resize([224, 224]),
                T.ToTensor(),
                T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        dataset = ImageDataset(df, root_path, transform=transform)
        
        dataset_type = "训练集" if use_train_set else "测试集"
        logger.info(f"{dataset_type} ImageDataset创建完成:")
        logger.info(f"  数据集大小: {len(dataset)}")
        logger.info(f"  类别数: {dataset.num_classes}")
        
        return dataset

class WildlifeToolsTrainer:
    """使用wildlife-tools的专业训练器"""
    
    def __init__(self, split_file: str = 'dataset_split.json', 
                 model_name: str = 'hf-hub:BVRA/MegaDescriptor-T-224',
                 loss_type: str = 'arcface'):
        
        self.split_file = split_file
        self.model_name = model_name
        self.loss_type = loss_type
        # GPU内存优化策略
        if torch.cuda.is_available():
            self.device = 'cuda'
            # 清理GPU缓存
            torch.cuda.empty_cache()
            # 设置内存分配策略
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        else:
            self.device = 'cpu'
        
        # 创建数据集
        self.dataset_creator = CatDatasetCreator(split_file)
        self.train_dataset = self.dataset_creator.create_image_dataset(use_train_set=True)
        self.test_dataset = self.dataset_creator.create_image_dataset(use_train_set=False)
        
        # 创建模型
        self.backbone = self._create_backbone()
        
        # 创建损失函数
        self.objective = self._create_objective()
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        self.scheduler = self._create_scheduler()
        
        logger.info(f"WildlifeToolsTrainer初始化完成")
        logger.info(f"  模型: {model_name}")
        logger.info(f"  损失函数: {loss_type}")
        logger.info(f"  设备: {self.device}")
        logger.info(f"  训练集大小: {len(self.train_dataset)}")
        logger.info(f"  类别数: {self.train_dataset.num_classes}")
    
    def _create_backbone(self):
        """创建骨干网络"""
        backbone = timm.create_model(self.model_name, num_classes=0, pretrained=True)
        backbone = backbone.to(self.device)
        
        # 获取特征维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
            features = backbone(dummy_input)
            self.feature_dim = features.shape[1]
        
        logger.info(f"骨干网络创建完成: 特征维度 {self.feature_dim}")
        return backbone
    
    def _create_objective(self):
        """创建损失函数"""
        if self.loss_type == 'arcface':
            objective = ArcFaceLoss(
                num_classes=self.train_dataset.num_classes,
                embedding_size=self.feature_dim,
                margin=0.5,
                scale=64
            )
        elif self.loss_type == 'triplet':
            objective = TripletLoss(
                margin=0.2,
                mining='semihard',
                distance='l2_squared'
            )
        else:
            raise ValueError(f"不支持的损失函数类型: {self.loss_type}")
        
        objective = objective.to(self.device)
        logger.info(f"损失函数创建完成: {self.loss_type}")
        return objective
    
    def _create_optimizer(self):
        """创建优化器"""
        # 组合骨干网络和损失函数的参数
        params = itertools.chain(self.backbone.parameters(), self.objective.parameters())

        # GPU内存优化：使用更小的学习率，更稳定
        optimizer = optim.SGD(
            params=params,
            lr=0.005,  # 降低学习率配合梯度累积
            momentum=0.9,
            weight_decay=1e-4
        )

        logger.info(f"优化器创建完成: SGD (GPU内存优化)")
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler = optim.lr_scheduler.StepLR(
            self.optimizer,
            step_size=10,
            gamma=0.1
        )
        
        logger.info(f"学习率调度器创建完成: StepLR")
        return scheduler
    
    def train(self, epochs: int = 20, save_path: str = 'wildlife_tools_model.pth'):
        """开始训练"""
        logger.info(f"开始训练: {epochs} epochs")

        # GPU内存优化
        if self.device == 'cuda':
            torch.cuda.empty_cache()
            # 显示GPU内存状态
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            logger.info(f"GPU总内存: {gpu_memory:.1f}GB")

            # 启用混合精度训练节省内存
            torch.backends.cudnn.benchmark = True

        # 设置随机种子确保可重现性
        set_seed(42)
        
        # 创建BasicTrainer
        trainer = BasicTrainer(
            dataset=self.train_dataset,
            model=self.backbone,
            objective=self.objective,
            optimizer=self.optimizer,
            scheduler=self.scheduler,
            epochs=epochs,
            device=self.device,
            batch_size=8,   # GPU内存优化：使用小批次
            num_workers=2,  # 减少worker数节省内存
            accumulation_steps=4  # 梯度累积模拟大批次效果
        )
        
        # 开始训练
        start_time = time.time()
        trainer.train()
        training_time = time.time() - start_time
        
        logger.info(f"训练完成! 用时: {training_time:.1f}秒")
        
        # 保存模型
        self.save_model(save_path)
        
        return trainer
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'backbone_state_dict': self.backbone.state_dict(),
            'objective_state_dict': self.objective.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.feature_dim,
            'model_name': self.model_name,
            'loss_type': self.loss_type,
            'num_classes': self.train_dataset.num_classes,
            'split_file': self.split_file
        }, save_path)
        
        logger.info(f"模型已保存: {save_path}")
    
    def evaluate(self, model_path: str = None):
        """评估模型"""
        if model_path:
            # 加载模型
            checkpoint = torch.load(model_path, map_location=self.device)
            self.backbone.load_state_dict(checkpoint['backbone_state_dict'])
        
        self.backbone.eval()
        
        logger.info("开始评估...")
        
        # 创建特征提取器
        extractor = DeepFeatures(self.backbone)
        
        # 提取训练集和测试集特征
        logger.info("提取训练集特征...")
        train_features = extractor(self.train_dataset)
        
        logger.info("提取测试集特征...")
        test_features = extractor(self.test_dataset)
        
        # 计算相似度
        logger.info("计算相似度...")
        similarity_function = CosineSimilarity()
        similarity = similarity_function(test_features, train_features)
        
        # 进行分类
        logger.info("进行分类...")
        classifier = KnnClassifier(k=1, database_labels=self.train_dataset.labels_string)
        predictions = classifier(similarity['cosine'])
        
        # 计算准确率
        accuracy = np.mean(self.test_dataset.labels_string == predictions)
        
        logger.info(f"评估完成!")
        logger.info(f"测试集准确率: {accuracy:.1%}")
        
        return {
            'accuracy': accuracy,
            'predictions': predictions,
            'true_labels': self.test_dataset.labels_string
        }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Wildlife-tools训练')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--model', type=str, default='hf-hub:BVRA/MegaDescriptor-T-224',
                       help='预训练模型名称')
    parser.add_argument('--loss', type=str, default='arcface', choices=['arcface', 'triplet'],
                       help='损失函数类型')
    parser.add_argument('--epochs', type=int, default=15,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='wildlife_tools_model.pth',
                       help='模型保存路径')
    parser.add_argument('--evaluate', action='store_true',
                       help='训练后进行评估')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = WildlifeToolsTrainer(
        split_file=args.split_file,
        model_name=args.model,
        loss_type=args.loss
    )
    
    # 开始训练
    basic_trainer = trainer.train(epochs=args.epochs, save_path=args.output)
    
    # 评估
    if args.evaluate:
        results = trainer.evaluate(args.output)
        
        print(f"\n🎉 Wildlife-tools训练和评估完成!")
        print(f"📊 测试集准确率: {results['accuracy']:.1%}")
        
        # 保存评估结果
        eval_results = {
            'accuracy': float(results['accuracy']),
            'model_name': args.model,
            'loss_type': args.loss,
            'epochs': args.epochs,
            'timestamp': time.time()
        }
        
        eval_path = args.output.replace('.pth', '_evaluation.json')
        with open(eval_path, 'w') as f:
            json.dump(eval_results, f, indent=2)
        
        logger.info(f"评估结果已保存: {eval_path}")

if __name__ == "__main__":
    main()
