#!/usr/bin/env python3
"""
生产就绪的猫咪识别器 - 优化版本
支持批量处理、模型热更新、性能监控
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from pathlib import Path
import time
import json
from collections import defaultdict, deque
from PIL import Image
import torchvision.transforms as transforms
import timm
from concurrent.futures import ThreadPoolExecutor
import threading
from dataclasses import dataclass
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class RecognitionResult:
    """识别结果数据类"""
    success: bool
    cat_id: Optional[str] = None
    cat_name: Optional[str] = None
    confidence: float = 0.0
    similarity: float = 0.0
    threshold_used: float = 0.0
    response_time: float = 0.0
    status: str = 'unknown'
    error: Optional[str] = None
    top_matches: List[Dict] = None

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    total_recognitions: int = 0
    successful_recognitions: int = 0
    avg_response_time: float = 0.0
    avg_confidence: float = 0.0
    accuracy: float = 0.0
    throughput: float = 0.0  # 每秒处理数量

class EnhancedMegaDescriptor(nn.Module):
    """增强版MegaDescriptor - 生产优化版"""
    
    def __init__(self, feature_dim=512):
        super().__init__()
        
        # 加载预训练的MegaDescriptor
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
    
    def forward(self, x):
        backbone_features = self.backbone(x)
        enhanced_features = self.feature_enhancer(backbone_features)
        return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)

class ProductionCatRecognizer:
    """生产级猫咪识别器"""
    
    def __init__(self, model_path: str = None, device: str = 'auto', 
                 batch_size: int = 8, max_workers: int = 4):
        # 设备选择
        if device == 'auto':
            self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            self.device = device
        
        self.batch_size = batch_size
        self.max_workers = max_workers
        
        # 加载模型
        self.model = self._load_model(model_path)
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 特征数据库
        self.feature_database = {}
        self.cat_database = {}
        
        # 性能监控
        self.metrics = PerformanceMetrics()
        self.response_times = deque(maxlen=1000)  # 保留最近1000次的响应时间
        
        # 线程安全锁
        self.lock = threading.RLock()
        
        # 配置参数
        self.similarity_threshold = 0.65
        
        logger.info(f"生产级识别器初始化完成 (设备: {self.device}, 批量大小: {batch_size})")
    
    def _load_model(self, model_path: str = None) -> nn.Module:
        """加载模型"""
        try:
            model = EnhancedMegaDescriptor(feature_dim=512).to(self.device)
            
            if model_path and os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"成功加载增强模型: {model_path}")
            else:
                logger.info("使用预训练的MegaDescriptor模型")
            
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def update_model(self, new_model_path: str) -> bool:
        """热更新模型"""
        try:
            with self.lock:
                new_model = self._load_model(new_model_path)
                self.model = new_model
                logger.info(f"模型热更新成功: {new_model_path}")
                return True
        except Exception as e:
            logger.error(f"模型热更新失败: {e}")
            return False
    
    def extract_features_batch(self, images: List[torch.Tensor]) -> np.ndarray:
        """批量特征提取"""
        try:
            with torch.no_grad():
                # 组成批次
                batch_tensor = torch.stack(images).to(self.device)
                features = self.model(batch_tensor)
                return features.cpu().numpy()
        except Exception as e:
            logger.error(f"批量特征提取失败: {e}")
            return np.random.randn(len(images), 512).astype(np.float32)
    
    def register_cat_batch(self, cat_data: List[Tuple[str, str, List[str]]]) -> List[Dict]:
        """批量注册猫咪"""
        results = []
        
        # 准备所有图片
        all_images = []
        image_to_cat = []
        
        for cat_id, cat_name, image_paths in cat_data:
            for img_path in image_paths:
                try:
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.transform(image)
                    all_images.append(image_tensor)
                    image_to_cat.append((cat_id, cat_name))
                except Exception as e:
                    logger.warning(f"加载图片失败 {img_path}: {e}")
        
        if not all_images:
            return [{'success': False, 'error': '没有有效图片'} for _ in cat_data]
        
        # 批量提取特征
        features_batch = self.extract_features_batch(all_images)
        
        # 组织特征到对应的猫咪
        cat_features = defaultdict(list)
        for i, (cat_id, cat_name) in enumerate(image_to_cat):
            cat_features[cat_id].append(features_batch[i])
        
        # 注册每只猫咪
        with self.lock:
            for cat_id, cat_name, image_paths in cat_data:
                if cat_id in cat_features and len(cat_features[cat_id]) >= 3:
                    # 保存特征
                    if cat_id not in self.feature_database:
                        self.feature_database[cat_id] = []
                    
                    self.feature_database[cat_id].extend(cat_features[cat_id])
                    
                    # 保存猫咪信息
                    self.cat_database[cat_id] = {
                        'name': cat_name,
                        'image_count': len(cat_features[cat_id]),
                        'registration_time': time.time()
                    }
                    
                    results.append({
                        'success': True,
                        'cat_id': cat_id,
                        'cat_name': cat_name,
                        'images_used': len(cat_features[cat_id])
                    })
                else:
                    results.append({
                        'success': False,
                        'cat_id': cat_id,
                        'error': f'有效图片不足: {len(cat_features.get(cat_id, []))}'
                    })
        
        return results
    
    def recognize_cat_batch(self, image_paths: List[str]) -> List[RecognitionResult]:
        """批量识别猫咪"""
        start_time = time.time()
        
        # 加载图片
        images = []
        valid_indices = []
        
        for i, img_path in enumerate(image_paths):
            try:
                image = Image.open(img_path).convert('RGB')
                image_tensor = self.transform(image)
                images.append(image_tensor)
                valid_indices.append(i)
            except Exception as e:
                logger.warning(f"加载图片失败 {img_path}: {e}")
        
        if not images:
            return [RecognitionResult(success=False, error="没有有效图片") 
                   for _ in image_paths]
        
        # 批量特征提取
        features_batch = self.extract_features_batch(images)
        
        # 批量识别
        results = []
        for i, img_path in enumerate(image_paths):
            if i in valid_indices:
                idx = valid_indices.index(i)
                query_features = features_batch[idx]
                result = self._recognize_single(query_features, img_path)
            else:
                result = RecognitionResult(success=False, error="图片加载失败")
            
            results.append(result)
        
        # 更新性能指标
        batch_time = time.time() - start_time
        self._update_metrics(results, batch_time)
        
        return results
    
    def _recognize_single(self, query_features: np.ndarray, image_path: str) -> RecognitionResult:
        """单个识别"""
        start_time = time.time()
        
        try:
            with self.lock:
                if not self.feature_database:
                    return RecognitionResult(
                        success=False,
                        status='no_cats',
                        error='没有注册的猫咪',
                        response_time=time.time() - start_time
                    )
                
                # 计算相似度
                best_match_id = None
                best_similarity = 0.0
                all_matches = []
                
                for cat_id, cat_features_list in self.feature_database.items():
                    similarities = []
                    for cat_features in cat_features_list:
                        sim = np.dot(query_features, cat_features)
                        similarities.append(sim)
                    
                    if similarities:
                        # 加权平均相似度
                        sorted_sims = sorted(similarities, reverse=True)
                        if len(sorted_sims) >= 3:
                            weights = np.array([0.5, 0.3, 0.2])
                            weighted_sim = np.average(sorted_sims[:3], weights=weights)
                        elif len(sorted_sims) == 2:
                            weights = np.array([0.7, 0.3])
                            weighted_sim = np.average(sorted_sims[:2], weights=weights)
                        else:
                            weighted_sim = sorted_sims[0]
                        
                        all_matches.append((cat_id, weighted_sim))
                        
                        if weighted_sim > best_similarity:
                            best_similarity = weighted_sim
                            best_match_id = cat_id
                
                # 排序匹配结果
                all_matches.sort(key=lambda x: x[1], reverse=True)
                
                # 获取自适应阈值
                adaptive_threshold = self._get_adaptive_threshold(len(self.cat_database))
                
                response_time = time.time() - start_time
                
                if best_similarity >= adaptive_threshold:
                    cat_info = self.cat_database[best_match_id]
                    confidence = min(0.99, best_similarity * 1.05)
                    
                    return RecognitionResult(
                        success=True,
                        cat_id=best_match_id,
                        cat_name=cat_info['name'],
                        confidence=confidence,
                        similarity=best_similarity,
                        threshold_used=adaptive_threshold,
                        response_time=response_time,
                        status='recognized',
                        top_matches=[
                            {
                                'cat_id': cat_id,
                                'cat_name': self.cat_database[cat_id]['name'],
                                'similarity': similarity
                            }
                            for cat_id, similarity in all_matches[:3]
                        ]
                    )
                else:
                    return RecognitionResult(
                        success=False,
                        similarity=best_similarity,
                        threshold_used=adaptive_threshold,
                        response_time=response_time,
                        status='unknown',
                        error=f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})'
                    )
                    
        except Exception as e:
            return RecognitionResult(
                success=False,
                error=str(e),
                response_time=time.time() - start_time
            )
    
    def _get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值"""
        base_threshold = self.similarity_threshold
        
        if num_cats <= 3:
            return base_threshold + 0.1
        elif num_cats <= 5:
            return base_threshold
        elif num_cats <= 10:
            return base_threshold - 0.05
        elif num_cats <= 20:
            return base_threshold - 0.1
        else:
            return max(0.4, base_threshold - 0.15)
    
    def _update_metrics(self, results: List[RecognitionResult], batch_time: float):
        """更新性能指标"""
        with self.lock:
            successful = sum(1 for r in results if r.success)
            
            self.metrics.total_recognitions += len(results)
            self.metrics.successful_recognitions += successful
            self.metrics.accuracy = (self.metrics.successful_recognitions / 
                                   max(self.metrics.total_recognitions, 1))
            
            # 更新响应时间
            for result in results:
                if result.response_time > 0:
                    self.response_times.append(result.response_time)
            
            if self.response_times:
                self.metrics.avg_response_time = np.mean(self.response_times)
            
            # 更新置信度
            confidences = [r.confidence for r in results if r.success and r.confidence > 0]
            if confidences:
                self.metrics.avg_confidence = (
                    self.metrics.avg_confidence * 0.9 + np.mean(confidences) * 0.1
                )
            
            # 计算吞吐量
            if batch_time > 0:
                self.metrics.throughput = len(results) / batch_time
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        with self.lock:
            return {
                'registered_cats': len(self.cat_database),
                'total_recognitions': self.metrics.total_recognitions,
                'successful_recognitions': self.metrics.successful_recognitions,
                'accuracy': self.metrics.accuracy,
                'avg_response_time': self.metrics.avg_response_time,
                'avg_confidence': self.metrics.avg_confidence,
                'throughput': self.metrics.throughput,
                'current_threshold': self._get_adaptive_threshold(len(self.cat_database)),
                'recent_response_times': {
                    'min': float(np.min(self.response_times)) if self.response_times else 0,
                    'max': float(np.max(self.response_times)) if self.response_times else 0,
                    'p95': float(np.percentile(self.response_times, 95)) if self.response_times else 0
                }
            }
    
    def save_state(self, filepath: str):
        """保存识别器状态"""
        state = {
            'cat_database': self.cat_database,
            'feature_database': {k: [f.tolist() for f in v] 
                               for k, v in self.feature_database.items()},
            'metrics': {
                'total_recognitions': self.metrics.total_recognitions,
                'successful_recognitions': self.metrics.successful_recognitions,
                'accuracy': self.metrics.accuracy,
                'avg_confidence': self.metrics.avg_confidence
            },
            'timestamp': datetime.now().isoformat()
        }
        
        with open(filepath, 'w') as f:
            json.dump(state, f, indent=2)
        
        logger.info(f"识别器状态已保存: {filepath}")
    
    def load_state(self, filepath: str):
        """加载识别器状态"""
        try:
            with open(filepath, 'r') as f:
                state = json.load(f)
            
            with self.lock:
                self.cat_database = state['cat_database']
                self.feature_database = {
                    k: [np.array(f) for f in v] 
                    for k, v in state['feature_database'].items()
                }
                
                if 'metrics' in state:
                    metrics = state['metrics']
                    self.metrics.total_recognitions = metrics.get('total_recognitions', 0)
                    self.metrics.successful_recognitions = metrics.get('successful_recognitions', 0)
                    self.metrics.accuracy = metrics.get('accuracy', 0.0)
                    self.metrics.avg_confidence = metrics.get('avg_confidence', 0.0)
            
            logger.info(f"识别器状态已加载: {filepath}")
            
        except Exception as e:
            logger.error(f"加载识别器状态失败: {e}")

def create_production_recognizer(model_path: str = None, **kwargs):
    """创建生产级识别器的工厂函数"""
    return ProductionCatRecognizer(model_path, **kwargs)
