{"optimizations": {"5": {"similarity_data": {"positive_similarities": [0.9687395095825195, 0.923678457736969, 0.9811844825744629, 0.9822189807891846, 0.7467145919799805, 0.9247573614120483, 0.9320389032363892, 0.9933276176452637, 0.9919581413269043, 0.945533275604248, 0.9571256637573242, 0.937025785446167, 0.9838783144950867, 0.9911394715309143], "negative_similarities": [0.6617781519889832, 0.5707305669784546, 0.7430034875869751, 0.667503833770752, 0.941336989402771, 0.5967465043067932, 0.8358217477798462, 0.8055442571640015, 0.8017107248306274, 0.9307265281677246, 0.7731796503067017, 0.34077370166778564, 0.1215757429599762, 0.7278536558151245, 0.510602593421936, 0.3553151488304138, -0.041233308613300323, 0.8448922634124756, 0.07275576889514923, 0.3377777934074402, 0.28145459294319153, 0.7397487759590149, 0.23280829191207886, 0.22260689735412598, 0.1363113820552826, 0.557001531124115, 0.5175991654396057, 0.7359210252761841, 0.27005547285079956, 0.45779523253440857, 0.42936229705810547], "num_cats": 5, "rounds": 3}, "optimization": {"optimal_threshold": 0.8500000000000005, "best_f1": 0.896551724137931, "best_precision": 0.8666666666666667, "best_recall": 0.9285714285714286, "best_accuracy": 0.9333333333333333, "pos_mean": 0.9470943255083901, "pos_std": 0.06090865321857223, "neg_mean": 0.5219051763415337, "neg_std": 0.2683181441696859, "separation": 0.42518914916685646, "all_results": [{"threshold": 0.3, "precision": 0.3783783783783784, "recall": 1.0, "f1": 0.5490196078431372, "accuracy": 0.4888888888888889}, {"threshold": 0.31, "precision": 0.3783783783783784, "recall": 1.0, "f1": 0.5490196078431372, "accuracy": 0.4888888888888889}, {"threshold": 0.32, "precision": 0.3783783783783784, "recall": 1.0, "f1": 0.5490196078431372, "accuracy": 0.4888888888888889}, {"threshold": 0.33, "precision": 0.3783783783783784, "recall": 1.0, "f1": 0.5490196078431372, "accuracy": 0.4888888888888889}, {"threshold": 0.34, "precision": 0.3888888888888889, "recall": 1.0, "f1": 0.56, "accuracy": 0.5111111111111111}, {"threshold": 0.35000000000000003, "precision": 0.4, "recall": 1.0, "f1": 0.5714285714285715, "accuracy": 0.5333333333333333}, {"threshold": 0.36000000000000004, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.37000000000000005, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.38000000000000006, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.39000000000000007, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.4000000000000001, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.4100000000000001, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.4200000000000001, "precision": 0.4117647058823529, "recall": 1.0, "f1": 0.5833333333333334, "accuracy": 0.5555555555555556}, {"threshold": 0.4300000000000001, "precision": 0.42424242424242425, "recall": 1.0, "f1": 0.5957446808510638, "accuracy": 0.5777777777777777}, {"threshold": 0.4400000000000001, "precision": 0.42424242424242425, "recall": 1.0, "f1": 0.5957446808510638, "accuracy": 0.5777777777777777}, {"threshold": 0.4500000000000001, "precision": 0.42424242424242425, "recall": 1.0, "f1": 0.5957446808510638, "accuracy": 0.5777777777777777}, {"threshold": 0.46000000000000013, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.47000000000000014, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.48000000000000015, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.49000000000000016, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.5000000000000002, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.5100000000000002, "precision": 0.4375, "recall": 1.0, "f1": 0.6086956521739131, "accuracy": 0.6}, {"threshold": 0.5200000000000002, "precision": 0.4666666666666667, "recall": 1.0, "f1": 0.6363636363636364, "accuracy": 0.6444444444444445}, {"threshold": 0.5300000000000002, "precision": 0.4666666666666667, "recall": 1.0, "f1": 0.6363636363636364, "accuracy": 0.6444444444444445}, {"threshold": 0.5400000000000003, "precision": 0.4666666666666667, "recall": 1.0, "f1": 0.6363636363636364, "accuracy": 0.6444444444444445}, {"threshold": 0.5500000000000003, "precision": 0.4666666666666667, "recall": 1.0, "f1": 0.6363636363636364, "accuracy": 0.6444444444444445}, {"threshold": 0.5600000000000003, "precision": 0.4827586206896552, "recall": 1.0, "f1": 0.6511627906976745, "accuracy": 0.6666666666666666}, {"threshold": 0.5700000000000003, "precision": 0.4827586206896552, "recall": 1.0, "f1": 0.6511627906976745, "accuracy": 0.6666666666666666}, {"threshold": 0.5800000000000003, "precision": 0.5, "recall": 1.0, "f1": 0.6666666666666666, "accuracy": 0.6888888888888889}, {"threshold": 0.5900000000000003, "precision": 0.5, "recall": 1.0, "f1": 0.6666666666666666, "accuracy": 0.6888888888888889}, {"threshold": 0.6000000000000003, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6100000000000003, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6200000000000003, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6300000000000003, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6400000000000003, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6500000000000004, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6600000000000004, "precision": 0.5185185185185185, "recall": 1.0, "f1": 0.6829268292682926, "accuracy": 0.7111111111111111}, {"threshold": 0.6700000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.6800000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.6900000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.7000000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.7100000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.7200000000000004, "precision": 0.56, "recall": 1.0, "f1": 0.717948717948718, "accuracy": 0.7555555555555555}, {"threshold": 0.7300000000000004, "precision": 0.5833333333333334, "recall": 1.0, "f1": 0.7368421052631579, "accuracy": 0.7777777777777778}, {"threshold": 0.7400000000000004, "precision": 0.6363636363636364, "recall": 1.0, "f1": 0.7777777777777778, "accuracy": 0.8222222222222222}, {"threshold": 0.7500000000000004, "precision": 0.65, "recall": 0.9285714285714286, "f1": 0.7647058823529412, "accuracy": 0.8222222222222222}, {"threshold": 0.7600000000000005, "precision": 0.65, "recall": 0.9285714285714286, "f1": 0.7647058823529412, "accuracy": 0.8222222222222222}, {"threshold": 0.7700000000000005, "precision": 0.65, "recall": 0.9285714285714286, "f1": 0.7647058823529412, "accuracy": 0.8222222222222222}, {"threshold": 0.7800000000000005, "precision": 0.6842105263157895, "recall": 0.9285714285714286, "f1": 0.7878787878787878, "accuracy": 0.8444444444444444}, {"threshold": 0.7900000000000005, "precision": 0.6842105263157895, "recall": 0.9285714285714286, "f1": 0.7878787878787878, "accuracy": 0.8444444444444444}, {"threshold": 0.8000000000000005, "precision": 0.6842105263157895, "recall": 0.9285714285714286, "f1": 0.7878787878787878, "accuracy": 0.8444444444444444}, {"threshold": 0.8100000000000005, "precision": 0.7647058823529411, "recall": 0.9285714285714286, "f1": 0.8387096774193549, "accuracy": 0.8888888888888888}, {"threshold": 0.8200000000000005, "precision": 0.7647058823529411, "recall": 0.9285714285714286, "f1": 0.8387096774193549, "accuracy": 0.8888888888888888}, {"threshold": 0.8300000000000005, "precision": 0.7647058823529411, "recall": 0.9285714285714286, "f1": 0.8387096774193549, "accuracy": 0.8888888888888888}, {"threshold": 0.8400000000000005, "precision": 0.8125, "recall": 0.9285714285714286, "f1": 0.8666666666666666, "accuracy": 0.9111111111111111}, {"threshold": 0.8500000000000005, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}, {"threshold": 0.8600000000000005, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}, {"threshold": 0.8700000000000006, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}, {"threshold": 0.8800000000000006, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}, {"threshold": 0.8900000000000006, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}, {"threshold": 0.9000000000000006, "precision": 0.8666666666666667, "recall": 0.9285714285714286, "f1": 0.896551724137931, "accuracy": 0.9333333333333333}]}}, "10": {"similarity_data": {"positive_similarities": [0.9580849409103394, 0.8648085594177246, 0.9098668098449707, 0.928733229637146, 0.8799399137496948, 0.9028255939483643, 0.8597660064697266, 0.8319033980369568, 0.9310828447341919, 0.9185478687286377, 0.6414808034896851, 0.8490163087844849, 0.9872023463249207, 0.8767849802970886, 0.9948824644088745, 0.9917389154434204, 0.769597053527832, 0.9452723264694214, 0.9936742782592773, 0.9800172448158264, 0.9413185715675354, 0.952560305595398, 0.5960652828216553, 0.9312478303909302, 0.962183952331543, 0.9727641344070435, 0.9113917350769043], "negative_similarities": [0.6658676862716675, 0.4650082290172577, 0.8400807976722717, 0.8289952278137207, 0.5468783974647522, 0.33006879687309265, 0.6944378614425659, 0.43827933073043823, 0.5981651544570923, 0.36999499797821045, 0.6882054805755615, 0.5487841963768005, 0.7104371190071106, 0.7023298144340515, 0.48178720474243164, 0.9419546723365784, 0.4918140769004822, 0.7900934219360352, 0.7661370635032654, 0.7252210378646851, 0.9018293023109436, 0.8722661733627319, 0.6295218467712402, 0.5565452575683594, 0.7533138394355774, 0.7152112126350403, 0.6596542596817017, 0.6273390054702759, 0.9012531042098999, 0.6678156852722168, 0.8712371587753296, 0.4104234576225281, 0.6300274133682251, 0.41234686970710754, 0.7661814093589783, 0.6870661377906799, 0.8623443245887756, 0.7507866621017456, 0.7840296626091003, 0.6689382195472717, 0.3722671866416931, 0.3534269332885742, 0.8971121311187744, 0.8111703991889954, 0.488384872674942, 0.27548497915267944, 0.8580279350280762, 0.3549010157585144, 0.3321930170059204, 0.07691823691129684, 0.7070082426071167, 0.45105797052383423, 0.5515537261962891, 0.5435307621955872, 0.6344031691551208, 0.6181519031524658, 0.5766078233718872, 0.8065557479858398, 0.7725100517272949, 0.8575822114944458, 0.606691837310791, 0.6073397397994995, 0.5712835192680359], "num_cats": 10, "rounds": 3}, "optimization": {"optimal_threshold": 0.8300000000000005, "best_f1": 0.7868852459016393, "best_precision": 0.7058823529411765, "best_recall": 0.8888888888888888, "best_accuracy": 0.8555555555555555, "pos_mean": 0.8993613962773923, "pos_std": 0.09619425815056763, "neg_mean": 0.632965634621325, "neg_std": 0.18320675113986779, "separation": 0.26639576165606726, "all_results": [{"threshold": 0.3, "precision": 0.3068181818181818, "recall": 1.0, "f1": 0.46956521739130436, "accuracy": 0.32222222222222224}, {"threshold": 0.31, "precision": 0.3068181818181818, "recall": 1.0, "f1": 0.46956521739130436, "accuracy": 0.32222222222222224}, {"threshold": 0.32, "precision": 0.3068181818181818, "recall": 1.0, "f1": 0.46956521739130436, "accuracy": 0.32222222222222224}, {"threshold": 0.33, "precision": 0.3068181818181818, "recall": 1.0, "f1": 0.46956521739130436, "accuracy": 0.32222222222222224}, {"threshold": 0.34, "precision": 0.313953488372093, "recall": 1.0, "f1": 0.47787610619469023, "accuracy": 0.34444444444444444}, {"threshold": 0.35000000000000003, "precision": 0.313953488372093, "recall": 1.0, "f1": 0.47787610619469023, "accuracy": 0.34444444444444444}, {"threshold": 0.36000000000000004, "precision": 0.32142857142857145, "recall": 1.0, "f1": 0.4864864864864865, "accuracy": 0.36666666666666664}, {"threshold": 0.37000000000000005, "precision": 0.3253012048192771, "recall": 1.0, "f1": 0.4909090909090909, "accuracy": 0.37777777777777777}, {"threshold": 0.38000000000000006, "precision": 0.32926829268292684, "recall": 1.0, "f1": 0.4954128440366972, "accuracy": 0.3888888888888889}, {"threshold": 0.39000000000000007, "precision": 0.32926829268292684, "recall": 1.0, "f1": 0.4954128440366972, "accuracy": 0.3888888888888889}, {"threshold": 0.4000000000000001, "precision": 0.32926829268292684, "recall": 1.0, "f1": 0.4954128440366972, "accuracy": 0.3888888888888889}, {"threshold": 0.4100000000000001, "precision": 0.32926829268292684, "recall": 1.0, "f1": 0.4954128440366972, "accuracy": 0.3888888888888889}, {"threshold": 0.4200000000000001, "precision": 0.3375, "recall": 1.0, "f1": 0.5046728971962617, "accuracy": 0.4111111111111111}, {"threshold": 0.4300000000000001, "precision": 0.3375, "recall": 1.0, "f1": 0.5046728971962617, "accuracy": 0.4111111111111111}, {"threshold": 0.4400000000000001, "precision": 0.34177215189873417, "recall": 1.0, "f1": 0.5094339622641509, "accuracy": 0.4222222222222222}, {"threshold": 0.4500000000000001, "precision": 0.34177215189873417, "recall": 1.0, "f1": 0.5094339622641509, "accuracy": 0.4222222222222222}, {"threshold": 0.46000000000000013, "precision": 0.34615384615384615, "recall": 1.0, "f1": 0.5142857142857142, "accuracy": 0.43333333333333335}, {"threshold": 0.47000000000000014, "precision": 0.35064935064935066, "recall": 1.0, "f1": 0.5192307692307693, "accuracy": 0.4444444444444444}, {"threshold": 0.48000000000000015, "precision": 0.35064935064935066, "recall": 1.0, "f1": 0.5192307692307693, "accuracy": 0.4444444444444444}, {"threshold": 0.49000000000000016, "precision": 0.36, "recall": 1.0, "f1": 0.5294117647058824, "accuracy": 0.4666666666666667}, {"threshold": 0.5000000000000002, "precision": 0.36486486486486486, "recall": 1.0, "f1": 0.5346534653465346, "accuracy": 0.4777777777777778}, {"threshold": 0.5100000000000002, "precision": 0.36486486486486486, "recall": 1.0, "f1": 0.5346534653465346, "accuracy": 0.4777777777777778}, {"threshold": 0.5200000000000002, "precision": 0.36486486486486486, "recall": 1.0, "f1": 0.5346534653465346, "accuracy": 0.4777777777777778}, {"threshold": 0.5300000000000002, "precision": 0.36486486486486486, "recall": 1.0, "f1": 0.5346534653465346, "accuracy": 0.4777777777777778}, {"threshold": 0.5400000000000003, "precision": 0.36486486486486486, "recall": 1.0, "f1": 0.5346534653465346, "accuracy": 0.4777777777777778}, {"threshold": 0.5500000000000003, "precision": 0.38028169014084506, "recall": 1.0, "f1": 0.5510204081632654, "accuracy": 0.5111111111111111}, {"threshold": 0.5600000000000003, "precision": 0.391304347826087, "recall": 1.0, "f1": 0.5625, "accuracy": 0.5333333333333333}, {"threshold": 0.5700000000000003, "precision": 0.391304347826087, "recall": 1.0, "f1": 0.5625, "accuracy": 0.5333333333333333}, {"threshold": 0.5800000000000003, "precision": 0.40298507462686567, "recall": 1.0, "f1": 0.5744680851063829, "accuracy": 0.5555555555555556}, {"threshold": 0.5900000000000003, "precision": 0.40298507462686567, "recall": 1.0, "f1": 0.5744680851063829, "accuracy": 0.5555555555555556}, {"threshold": 0.6000000000000003, "precision": 0.4, "recall": 0.9629629629629629, "f1": 0.5652173913043479, "accuracy": 0.5555555555555556}, {"threshold": 0.6100000000000003, "precision": 0.4126984126984127, "recall": 0.9629629629629629, "f1": 0.5777777777777777, "accuracy": 0.5777777777777777}, {"threshold": 0.6200000000000003, "precision": 0.41935483870967744, "recall": 0.9629629629629629, "f1": 0.5842696629213483, "accuracy": 0.5888888888888889}, {"threshold": 0.6300000000000003, "precision": 0.43333333333333335, "recall": 0.9629629629629629, "f1": 0.5977011494252873, "accuracy": 0.6111111111111112}, {"threshold": 0.6400000000000003, "precision": 0.4482758620689655, "recall": 0.9629629629629629, "f1": 0.6117647058823529, "accuracy": 0.6333333333333333}, {"threshold": 0.6500000000000004, "precision": 0.43859649122807015, "recall": 0.9259259259259259, "f1": 0.5952380952380952, "accuracy": 0.6222222222222222}, {"threshold": 0.6600000000000004, "precision": 0.44642857142857145, "recall": 0.9259259259259259, "f1": 0.6024096385542168, "accuracy": 0.6333333333333333}, {"threshold": 0.6700000000000004, "precision": 0.4716981132075472, "recall": 0.9259259259259259, "f1": 0.625, "accuracy": 0.6666666666666666}, {"threshold": 0.6800000000000004, "precision": 0.4716981132075472, "recall": 0.9259259259259259, "f1": 0.625, "accuracy": 0.6666666666666666}, {"threshold": 0.6900000000000004, "precision": 0.49019607843137253, "recall": 0.9259259259259259, "f1": 0.6410256410256411, "accuracy": 0.6888888888888889}, {"threshold": 0.7000000000000004, "precision": 0.5, "recall": 0.9259259259259259, "f1": 0.6493506493506493, "accuracy": 0.7}, {"threshold": 0.7100000000000004, "precision": 0.5208333333333334, "recall": 0.9259259259259259, "f1": 0.6666666666666667, "accuracy": 0.7222222222222222}, {"threshold": 0.7200000000000004, "precision": 0.5434782608695652, "recall": 0.9259259259259259, "f1": 0.6849315068493151, "accuracy": 0.7444444444444445}, {"threshold": 0.7300000000000004, "precision": 0.5555555555555556, "recall": 0.9259259259259259, "f1": 0.6944444444444444, "accuracy": 0.7555555555555555}, {"threshold": 0.7400000000000004, "precision": 0.5555555555555556, "recall": 0.9259259259259259, "f1": 0.6944444444444444, "accuracy": 0.7555555555555555}, {"threshold": 0.7500000000000004, "precision": 0.5555555555555556, "recall": 0.9259259259259259, "f1": 0.6944444444444444, "accuracy": 0.7555555555555555}, {"threshold": 0.7600000000000005, "precision": 0.5813953488372093, "recall": 0.9259259259259259, "f1": 0.7142857142857142, "accuracy": 0.7777777777777778}, {"threshold": 0.7700000000000005, "precision": 0.6, "recall": 0.8888888888888888, "f1": 0.7164179104477612, "accuracy": 0.7888888888888889}, {"threshold": 0.7800000000000005, "precision": 0.6153846153846154, "recall": 0.8888888888888888, "f1": 0.7272727272727274, "accuracy": 0.8}, {"threshold": 0.7900000000000005, "precision": 0.631578947368421, "recall": 0.8888888888888888, "f1": 0.7384615384615385, "accuracy": 0.8111111111111111}, {"threshold": 0.8000000000000005, "precision": 0.6486486486486487, "recall": 0.8888888888888888, "f1": 0.75, "accuracy": 0.8222222222222222}, {"threshold": 0.8100000000000005, "precision": 0.6666666666666666, "recall": 0.8888888888888888, "f1": 0.761904761904762, "accuracy": 0.8333333333333334}, {"threshold": 0.8200000000000005, "precision": 0.6857142857142857, "recall": 0.8888888888888888, "f1": 0.7741935483870968, "accuracy": 0.8444444444444444}, {"threshold": 0.8300000000000005, "precision": 0.7058823529411765, "recall": 0.8888888888888888, "f1": 0.7868852459016393, "accuracy": 0.8555555555555555}, {"threshold": 0.8400000000000005, "precision": 0.696969696969697, "recall": 0.8518518518518519, "f1": 0.7666666666666667, "accuracy": 0.8444444444444444}, {"threshold": 0.8500000000000005, "precision": 0.7096774193548387, "recall": 0.8148148148148148, "f1": 0.7586206896551724, "accuracy": 0.8444444444444444}, {"threshold": 0.8600000000000005, "precision": 0.75, "recall": 0.7777777777777778, "f1": 0.7636363636363638, "accuracy": 0.8555555555555555}, {"threshold": 0.8700000000000006, "precision": 0.7692307692307693, "recall": 0.7407407407407407, "f1": 0.7547169811320754, "accuracy": 0.8555555555555555}, {"threshold": 0.8800000000000006, "precision": 0.8181818181818182, "recall": 0.6666666666666666, "f1": 0.7346938775510203, "accuracy": 0.8555555555555555}, {"threshold": 0.8900000000000006, "precision": 0.8181818181818182, "recall": 0.6666666666666666, "f1": 0.7346938775510203, "accuracy": 0.8555555555555555}, {"threshold": 0.9000000000000006, "precision": 0.8571428571428571, "recall": 0.6666666666666666, "f1": 0.75, "accuracy": 0.8666666666666667}]}}, "20": {"similarity_data": {"positive_similarities": [0.8830561637878418, 0.9751883745193481, 0.9915034770965576, 0.8406552076339722, 0.925041913986206, 0.9729942083358765, 0.9770084619522095, 0.9391309022903442, 0.9509916305541992, 0.966866135597229, 0.9793546795845032, 0.9811869859695435, 0.8889999985694885, 0.9706162214279175, 0.9689716100692749, 0.9606319665908813, 0.8808498978614807, 0.9564588069915771, 0.9785627126693726, 0.8210717439651489, 0.9867479205131531, 0.8676310777664185, 0.9177930951118469, 0.9321811199188232, 0.9468764066696167, 0.9797983765602112, 0.8705627918243408, 0.944534420967102, 0.8386953473091125, 0.9952617883682251, 0.9680876731872559, 0.9823346138000488, 0.9821239709854126, 0.9446732401847839, 0.9912055730819702, 0.607970118522644, 0.9620569944381714, 0.8047550916671753, 0.9477262496948242, 0.8889999985694885, 0.9662063121795654, 0.8635003566741943, 0.9825565814971924, 0.9431286454200745, 0.8897910118103027, 0.9618542194366455, 0.9887353777885437, 0.9139190912246704, 0.9523496627807617, 0.8952270746231079, 0.7975464463233948, 0.9137418270111084, 0.9328311681747437, 0.9347965121269226, 0.9915034770965576, 0.9250853061676025, 0.941119372844696, 0.9124385118484497], "negative_similarities": [0.767633318901062, 0.7498976588249207, 0.9268592000007629, 0.8661621809005737, 0.9120622277259827, 0.8016870021820068, 0.7883980870246887, 0.6575174927711487, 0.8606084585189819, 0.8497244715690613, 0.8979209661483765, 0.8228871822357178, 0.829720139503479, 0.6558351516723633, 0.8396518230438232, 0.7942652702331543, 0.7120872735977173, 0.5131506323814392, 0.8135040998458862, 0.8077991008758545, 0.9811776876449585, 0.9132319092750549, 0.8273249864578247, 0.819197952747345, 0.7476606369018555, 0.5933898091316223, 0.9183967113494873, 0.9070261716842651, 0.9108752012252808, 0.8177862763404846, 0.8212712407112122, 0.5899649262428284, 0.5526789426803589, 0.9555640816688538, 0.9460749626159668, 0.8756349086761475, 0.5321157574653625, 0.3669736683368683, 0.8828561305999756, 0.8692381978034973, 0.8333871364593506, 0.6820352077484131, 0.7840451002120972, 0.764864444732666, 0.6055973768234253, 0.5074517130851746, 0.6865128874778748, 0.6016096472740173, 0.8126095533370972, 0.7446146011352539, 0.6910531520843506, 0.6180524826049805, 0.811661422252655, 0.7995942831039429, 0.880607008934021, 0.8612172603607178, 0.901496410369873, 0.856784462928772, 0.6850267648696899, 0.660972535610199, 0.7347695231437683, 0.6916496753692627, 0.8942249417304993, 0.8011987805366516, 0.7873303890228271, 0.7070754170417786, 0.9246871471405029, 0.8499077558517456, 0.7982124090194702, 0.7792232632637024, 0.826644778251648, 0.6436300873756409, 0.9098243117332458, 0.7584031820297241, 0.7696197628974915, 0.7570415735244751, 0.4953141510486603, 0.4383888840675354, 0.8822329044342041, 0.6968652009963989, 0.9431215524673462, 0.6525580286979675, 0.9211071729660034, 0.863129734992981, 0.8811104893684387, 0.8801500201225281, 0.6438077688217163, 0.4949578046798706, 0.813389241695404, 0.6832945346832275, 0.7865656614303589, 0.7625396847724915, 0.7848227620124817, 0.7620992064476013, 0.8138852715492249, 0.7792959213256836, 0.9257733821868896, 0.8864585161209106, 0.5361374616622925, 0.4065772294998169, 0.934828519821167, 0.8833048343658447, 0.9329752922058105, 0.8652636408805847, 0.7829140424728394, 0.7382054328918457, 0.7294353246688843, 0.6002020239830017, 0.9464526772499084, 0.9063020944595337, 0.8759685158729553, 0.789871096611023, 0.8960089683532715, 0.8620012402534485, 0.8076423406600952, 0.7863662838935852, 0.7479283809661865, 0.6940515041351318, 0.7572434544563293, 0.7193107008934021, 0.7498853206634521, 0.6761595010757446], "num_cats": 20, "rounds": 3}, "optimization": {"optimal_threshold": 0.8800000000000006, "best_f1": 0.7205882352941176, "best_precision": 0.6282051282051282, "best_recall": 0.8448275862068966, "best_accuracy": 0.7888888888888889, "pos_mean": 0.9288877228210712, "pos_std": 0.06546010375782443, "neg_mean": 0.7748717387191585, "neg_std": 0.12624588251910782, "separation": 0.15401598410191275, "all_results": [{"threshold": 0.3, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.31, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.32, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.33, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.34, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.35000000000000003, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.36000000000000004, "precision": 0.32222222222222224, "recall": 1.0, "f1": 0.4873949579831933, "accuracy": 0.32222222222222224}, {"threshold": 0.37000000000000005, "precision": 0.3240223463687151, "recall": 1.0, "f1": 0.48945147679324896, "accuracy": 0.3277777777777778}, {"threshold": 0.38000000000000006, "precision": 0.3240223463687151, "recall": 1.0, "f1": 0.48945147679324896, "accuracy": 0.3277777777777778}, {"threshold": 0.39000000000000007, "precision": 0.3240223463687151, "recall": 1.0, "f1": 0.48945147679324896, "accuracy": 0.3277777777777778}, {"threshold": 0.4000000000000001, "precision": 0.3240223463687151, "recall": 1.0, "f1": 0.48945147679324896, "accuracy": 0.3277777777777778}, {"threshold": 0.4100000000000001, "precision": 0.3258426966292135, "recall": 1.0, "f1": 0.4915254237288135, "accuracy": 0.3333333333333333}, {"threshold": 0.4200000000000001, "precision": 0.3258426966292135, "recall": 1.0, "f1": 0.4915254237288135, "accuracy": 0.3333333333333333}, {"threshold": 0.4300000000000001, "precision": 0.3258426966292135, "recall": 1.0, "f1": 0.4915254237288135, "accuracy": 0.3333333333333333}, {"threshold": 0.4400000000000001, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.4500000000000001, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.46000000000000013, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.47000000000000014, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.48000000000000015, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.49000000000000016, "precision": 0.327683615819209, "recall": 1.0, "f1": 0.49361702127659574, "accuracy": 0.3388888888888889}, {"threshold": 0.5000000000000002, "precision": 0.3314285714285714, "recall": 1.0, "f1": 0.49785407725321884, "accuracy": 0.35}, {"threshold": 0.5100000000000002, "precision": 0.3333333333333333, "recall": 1.0, "f1": 0.5, "accuracy": 0.35555555555555557}, {"threshold": 0.5200000000000002, "precision": 0.3352601156069364, "recall": 1.0, "f1": 0.5021645021645021, "accuracy": 0.3611111111111111}, {"threshold": 0.5300000000000002, "precision": 0.3352601156069364, "recall": 1.0, "f1": 0.5021645021645021, "accuracy": 0.3611111111111111}, {"threshold": 0.5400000000000003, "precision": 0.3391812865497076, "recall": 1.0, "f1": 0.5065502183406114, "accuracy": 0.37222222222222223}, {"threshold": 0.5500000000000003, "precision": 0.3391812865497076, "recall": 1.0, "f1": 0.5065502183406114, "accuracy": 0.37222222222222223}, {"threshold": 0.5600000000000003, "precision": 0.3411764705882353, "recall": 1.0, "f1": 0.5087719298245613, "accuracy": 0.37777777777777777}, {"threshold": 0.5700000000000003, "precision": 0.3411764705882353, "recall": 1.0, "f1": 0.5087719298245613, "accuracy": 0.37777777777777777}, {"threshold": 0.5800000000000003, "precision": 0.3411764705882353, "recall": 1.0, "f1": 0.5087719298245613, "accuracy": 0.37777777777777777}, {"threshold": 0.5900000000000003, "precision": 0.3431952662721893, "recall": 1.0, "f1": 0.5110132158590308, "accuracy": 0.38333333333333336}, {"threshold": 0.6000000000000003, "precision": 0.34523809523809523, "recall": 1.0, "f1": 0.5132743362831858, "accuracy": 0.3888888888888889}, {"threshold": 0.6100000000000003, "precision": 0.3475609756097561, "recall": 0.9827586206896551, "f1": 0.5135135135135135, "accuracy": 0.4}, {"threshold": 0.6200000000000003, "precision": 0.3496932515337423, "recall": 0.9827586206896551, "f1": 0.5158371040723981, "accuracy": 0.40555555555555556}, {"threshold": 0.6300000000000003, "precision": 0.3496932515337423, "recall": 0.9827586206896551, "f1": 0.5158371040723981, "accuracy": 0.40555555555555556}, {"threshold": 0.6400000000000003, "precision": 0.3496932515337423, "recall": 0.9827586206896551, "f1": 0.5158371040723981, "accuracy": 0.40555555555555556}, {"threshold": 0.6500000000000004, "precision": 0.35403726708074534, "recall": 0.9827586206896551, "f1": 0.5205479452054794, "accuracy": 0.4166666666666667}, {"threshold": 0.6600000000000004, "precision": 0.36075949367088606, "recall": 0.9827586206896551, "f1": 0.5277777777777778, "accuracy": 0.43333333333333335}, {"threshold": 0.6700000000000004, "precision": 0.3630573248407643, "recall": 0.9827586206896551, "f1": 0.5302325581395348, "accuracy": 0.4388888888888889}, {"threshold": 0.6800000000000004, "precision": 0.36538461538461536, "recall": 0.9827586206896551, "f1": 0.5327102803738318, "accuracy": 0.4444444444444444}, {"threshold": 0.6900000000000004, "precision": 0.375, "recall": 0.9827586206896551, "f1": 0.5428571428571428, "accuracy": 0.4666666666666667}, {"threshold": 0.7000000000000004, "precision": 0.38513513513513514, "recall": 0.9827586206896551, "f1": 0.5533980582524272, "accuracy": 0.4888888888888889}, {"threshold": 0.7100000000000004, "precision": 0.3877551020408163, "recall": 0.9827586206896551, "f1": 0.5560975609756098, "accuracy": 0.49444444444444446}, {"threshold": 0.7200000000000004, "precision": 0.3931034482758621, "recall": 0.9827586206896551, "f1": 0.5615763546798029, "accuracy": 0.5055555555555555}, {"threshold": 0.7300000000000004, "precision": 0.3958333333333333, "recall": 0.9827586206896551, "f1": 0.5643564356435643, "accuracy": 0.5111111111111111}, {"threshold": 0.7400000000000004, "precision": 0.4014084507042254, "recall": 0.9827586206896551, "f1": 0.5700000000000001, "accuracy": 0.5222222222222223}, {"threshold": 0.7500000000000004, "precision": 0.41605839416058393, "recall": 0.9827586206896551, "f1": 0.5846153846153846, "accuracy": 0.55}, {"threshold": 0.7600000000000005, "precision": 0.4253731343283582, "recall": 0.9827586206896551, "f1": 0.59375, "accuracy": 0.5666666666666667}, {"threshold": 0.7700000000000005, "precision": 0.4418604651162791, "recall": 0.9827586206896551, "f1": 0.6096256684491979, "accuracy": 0.5944444444444444}, {"threshold": 0.7800000000000005, "precision": 0.44881889763779526, "recall": 0.9827586206896551, "f1": 0.6162162162162161, "accuracy": 0.6055555555555555}, {"threshold": 0.7900000000000005, "precision": 0.4789915966386555, "recall": 0.9827586206896551, "f1": 0.6440677966101696, "accuracy": 0.65}, {"threshold": 0.8000000000000005, "precision": 0.48695652173913045, "recall": 0.9655172413793104, "f1": 0.6473988439306358, "accuracy": 0.6611111111111111}, {"threshold": 0.8100000000000005, "precision": 0.5, "recall": 0.9482758620689655, "f1": 0.6547619047619048, "accuracy": 0.6777777777777778}, {"threshold": 0.8200000000000005, "precision": 0.5339805825242718, "recall": 0.9482758620689655, "f1": 0.6832298136645963, "accuracy": 0.7166666666666667}, {"threshold": 0.8300000000000005, "precision": 0.5567010309278351, "recall": 0.9310344827586207, "f1": 0.696774193548387, "accuracy": 0.7388888888888889}, {"threshold": 0.8400000000000005, "precision": 0.5638297872340425, "recall": 0.9137931034482759, "f1": 0.6973684210526316, "accuracy": 0.7444444444444445}, {"threshold": 0.8500000000000005, "precision": 0.5714285714285714, "recall": 0.896551724137931, "f1": 0.6979865771812079, "accuracy": 0.75}, {"threshold": 0.8600000000000005, "precision": 0.5777777777777777, "recall": 0.896551724137931, "f1": 0.7027027027027027, "accuracy": 0.7555555555555555}, {"threshold": 0.8700000000000006, "precision": 0.6172839506172839, "recall": 0.8620689655172413, "f1": 0.7194244604316548, "accuracy": 0.7833333333333333}, {"threshold": 0.8800000000000006, "precision": 0.6282051282051282, "recall": 0.8448275862068966, "f1": 0.7205882352941176, "accuracy": 0.7888888888888889}, {"threshold": 0.8900000000000006, "precision": 0.6666666666666666, "recall": 0.7586206896551724, "f1": 0.7096774193548386, "accuracy": 0.8}, {"threshold": 0.9000000000000006, "precision": 0.6935483870967742, "recall": 0.7413793103448276, "f1": 0.7166666666666668, "accuracy": 0.8111111111111111}]}}}, "adaptive_function_code": "def get_optimized_adaptive_threshold(num_cats: int) -> float:\n    \"\"\"优化后的自适应阈值函数\"\"\"\n    # 基于实际测试数据优化的阈值\n    scale_thresholds = {\n        5: 0.850,\n        10: 0.830,\n        20: 0.880,\n    }\n    \n    # 如果正好匹配某个规模\n    if num_cats in scale_thresholds:\n        return scale_thresholds[num_cats]\n    \n    # 线性插值\n    sorted_scales = sorted(scale_thresholds.keys())\n    \n    if num_cats <= sorted_scales[0]:\n        return scale_thresholds[sorted_scales[0]]\n    elif num_cats >= sorted_scales[-1]:\n        return scale_thresholds[sorted_scales[-1]]\n    else:\n        # 找到相邻的两个规模进行插值\n        for i in range(len(sorted_scales) - 1):\n            if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:\n                x1, y1 = sorted_scales[i], scale_thresholds[sorted_scales[i]]\n                x2, y2 = sorted_scales[i + 1], scale_thresholds[sorted_scales[i + 1]]\n                \n                # 线性插值\n                return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)\n    \n    # 默认值\n    return 0.65", "summary": {"scales": [5, 10, 20], "optimal_thresholds": {"5": 0.8500000000000005, "10": 0.8300000000000005, "20": 0.8800000000000006}, "f1_scores": {"5": 0.896551724137931, "10": 0.7868852459016393, "20": 0.7205882352941176}}}