#!/usr/bin/env python3
"""
综合验证 - 多轮随机测试验证1024维模型的真实性能
"""

import os
import sys
import random
import numpy as np
from pathlib import Path
import time
import logging
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from model_validator import validate_model

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def comprehensive_validation(model_path: str, dataset_path: str, 
                           scales: list = [5, 10, 20], rounds: int = 10):
    """综合验证 - 多轮随机测试"""
    
    print("🎯 综合验证测试")
    print("=" * 80)
    print(f"模型: {model_path}")
    print(f"测试规模: {scales}")
    print(f"每个规模轮数: {rounds}")
    print(f"总测试次数: {len(scales) * rounds}")
    
    all_results = {}
    
    for scale in scales:
        print(f"\n🔍 测试规模: {scale} 只猫咪")
        print("-" * 50)
        
        scale_results = []
        
        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮...", end=" ")
            
            try:
                result = validate_model(
                    model_path=model_path,
                    dataset_path=dataset_path,
                    num_cats=scale,
                    strategy='balanced'
                )
                
                scale_results.append(result)
                print(f"准确率: {result['accuracy']:.1%}")
                
            except Exception as e:
                print(f"失败: {e}")
                continue
        
        if scale_results:
            # 计算统计
            accuracies = [r['accuracy'] for r in scale_results]
            avg_accuracy = np.mean(accuracies)
            accuracy_std = np.std(accuracies)
            min_accuracy = np.min(accuracies)
            max_accuracy = np.max(accuracies)
            
            # 计算达标率
            target_95_count = sum(1 for acc in accuracies if acc >= 0.95)
            target_90_count = sum(1 for acc in accuracies if acc >= 0.90)
            
            all_results[scale] = {
                'avg_accuracy': avg_accuracy,
                'accuracy_std': accuracy_std,
                'min_accuracy': min_accuracy,
                'max_accuracy': max_accuracy,
                'target_95_rate': target_95_count / len(accuracies),
                'target_90_rate': target_90_count / len(accuracies),
                'rounds': scale_results,
                'total_rounds': len(scale_results)
            }
            
            print(f"📊 规模 {scale} 统计:")
            print(f"   平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
            print(f"   准确率范围: {min_accuracy:.1%} - {max_accuracy:.1%}")
            print(f"   达到95%+: {target_95_count}/{len(accuracies)} ({target_95_count/len(accuracies):.1%})")
            print(f"   达到90%+: {target_90_count}/{len(accuracies)} ({target_90_count/len(accuracies):.1%})")
    
    return all_results

def display_final_conclusion(results: dict):
    """显示最终结论"""
    print("\n" + "=" * 100)
    print("🎯 最终验证结论")
    print("=" * 100)
    
    if not results:
        print("没有有效的测试结果")
        return
    
    # 总体表现表格
    print(f"{'规模':<6} {'平均准确率':<12} {'准确率范围':<15} {'95%+达标率':<10} {'90%+达标率':<10} {'评级':<8}")
    print("-" * 80)
    
    overall_performance = []
    target_95_achieved = 0
    target_90_achieved = 0
    
    for scale in sorted(results.keys()):
        result = results[scale]
        
        avg_acc = result['avg_accuracy']
        min_acc = result['min_accuracy']
        max_acc = result['max_accuracy']
        rate_95 = result['target_95_rate']
        rate_90 = result['target_90_rate']
        
        # 评级
        if avg_acc >= 0.95 and rate_95 >= 0.8:
            grade = "A+"
            target_95_achieved += 1
        elif avg_acc >= 0.90 and rate_90 >= 0.8:
            grade = "A"
            target_90_achieved += 1
        elif avg_acc >= 0.85:
            grade = "B+"
        elif avg_acc >= 0.80:
            grade = "B"
        else:
            grade = "C"
        
        overall_performance.append(avg_acc)
        
        print(f"{scale:<6} {avg_acc:<12.1%} {min_acc:.1%}-{max_acc:.1%}{'':>6} "
              f"{rate_95:<10.1%} {rate_90:<10.1%} {grade:<8}")
    
    # 总体评价
    total_scales = len(results)
    avg_overall = np.mean(overall_performance)
    
    print(f"\n🏆 总体评价:")
    print(f"   测试规模数: {total_scales}")
    print(f"   平均准确率: {avg_overall:.1%}")
    print(f"   95%+目标达成: {target_95_achieved}/{total_scales} ({target_95_achieved/total_scales:.1%})")
    print(f"   90%+目标达成: {target_90_achieved}/{total_scales} ({target_90_achieved/total_scales:.1%})")
    
    # 最终结论
    print(f"\n🎯 验证结论:")
    
    if target_95_achieved == total_scales:
        conclusion = "🌟 完美达成 - 所有规模都稳定达到95%+目标!"
        recommendation = "✅ 模型已达到生产级别，可以部署使用"
    elif target_95_achieved >= total_scales * 0.8:
        conclusion = "🎉 基本达成 - 大部分规模达到95%+目标"
        recommendation = "📈 可以部署，但建议继续优化未达标规模"
    elif target_90_achieved == total_scales:
        conclusion = "✅ 良好表现 - 所有规模都达到90%+目标"
        recommendation = "🔧 需要进一步优化以达到95%+目标"
    elif avg_overall >= 0.85:
        conclusion = "📊 中等表现 - 平均准确率85%+"
        recommendation = "⚠️ 需要显著改进特征提取能力"
    else:
        conclusion = "⚠️ 表现不足 - 距离目标还有较大差距"
        recommendation = "🔄 建议重新设计训练策略"
    
    print(f"   {conclusion}")
    print(f"   建议: {recommendation}")
    
    # 与之前结果对比
    print(f"\n📊 与之前512维模型对比:")
    print(f"   之前随机测试结果:")
    print(f"     5只猫咪: 100.0% (完美)")
    print(f"     10只猫咪: 84.0% (波动大)")
    print(f"     20只猫咪: 89.0% (不稳定)")
    
    if 5 in results and 10 in results and 20 in results:
        current_5 = results[5]['avg_accuracy']
        current_10 = results[10]['avg_accuracy']
        current_20 = results[20]['avg_accuracy']
        
        print(f"   当前1024维模型:")
        print(f"     5只猫咪: {current_5:.1%} ({current_5-1.0:+.1%})")
        print(f"     10只猫咪: {current_10:.1%} ({current_10-0.84:+.1%})")
        print(f"     20只猫咪: {current_20:.1%} ({current_20-0.89:+.1%})")
        
        overall_improvement = (current_5 + current_10 + current_20) / 3 - (1.0 + 0.84 + 0.89) / 3
        
        if overall_improvement > 0.02:
            improvement_conclusion = "✅ 1024维模型确实有显著改进"
        elif overall_improvement > -0.02:
            improvement_conclusion = "⚖️ 1024维模型改进有限"
        else:
            improvement_conclusion = "❌ 1024维模型未能带来预期改进"
        
        print(f"   总体改进: {overall_improvement:+.1%}")
        print(f"   结论: {improvement_conclusion}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='综合验证测试')
    parser.add_argument('--model', type=str, default='fast_megadescriptor_100cats_best.pth',
                       help='模型路径')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=10,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='comprehensive_validation_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 运行综合验证
    start_time = time.time()
    results = comprehensive_validation(args.model, args.dataset, args.scales, args.rounds)
    end_time = time.time()
    
    # 显示结论
    display_final_conclusion(results)
    
    # 保存结果
    final_results = {
        'test_info': {
            'model': args.model,
            'scales': args.scales,
            'rounds_per_scale': args.rounds,
            'total_time': end_time - start_time,
            'timestamp': time.time()
        },
        'results': results
    }
    
    with open(args.output, 'w') as f:
        json.dump(final_results, f, indent=2, default=str)
    
    print(f"\n💾 综合验证结果已保存: {args.output}")
    print(f"⏱️ 总测试时间: {end_time - start_time:.1f}秒")

if __name__ == "__main__":
    main()
