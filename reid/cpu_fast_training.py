#!/usr/bin/env python3
"""
CPU快速训练验证 - 确保能够运行并验证效果
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from adaptive_recognizer import create_adaptive_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleTripletDataset(Dataset):
    """简化的三元组数据集"""
    
    def __init__(self, dataset_path: str, max_cats: int = 50):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        
        # 简化的数据增强
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.cat_images = defaultdict(list)
        self.cat_ids = []
        self._build_dataset()
        
        logger.info(f"简化数据集构建完成: {len(self.cat_ids)} 只猫咪")
    
    def _build_dataset(self):
        """构建数据集"""
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                valid_cats.append((cat_folder.name, images))
        
        # 按图片数量排序
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        selected_cats = valid_cats[:self.max_cats]
        
        for cat_id, images in selected_cats:
            self.cat_images[cat_id] = [str(img) for img in images]
            self.cat_ids.append(cat_id)
        
        logger.info(f"选择了 {len(selected_cats)} 只猫咪，总图片: {sum(len(imgs) for imgs in self.cat_images.values())}")
    
    def __len__(self):
        return len(self.cat_ids) * 30   # 小样本数
    
    def __getitem__(self, idx):
        """生成三元组"""
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择anchor和positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative
        negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative
        except Exception as e:
            return self.__getitem__(random.randint(0, len(self) - 1))

class SimpleMegaDescriptor(nn.Module):
    """简化的MegaDescriptor - 确保能在CPU上运行"""
    
    def __init__(self, feature_dim=512):  # 降低到512维
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 简化的特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
        
        logger.info(f"简化MegaDescriptor初始化: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        backbone_features = self.backbone(x)
        enhanced_features = self.feature_enhancer(backbone_features)
        return torch.nn.functional.normalize(enhanced_features, p=2, dim=1)

class CPUTrainer:
    """CPU训练器 - 确保稳定运行"""
    
    def __init__(self, dataset_path: str, max_cats: int = 50):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = 'cpu'  # 强制使用CPU
        
        # 数据集和数据加载器
        self.dataset = SimpleTripletDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=4,   # 小批次
            shuffle=True, 
            num_workers=2   # 少量worker
        )
        
        # 模型
        self.model = SimpleMegaDescriptor(feature_dim=512).to(self.device)
        
        # 损失函数
        self.criterion = nn.TripletMarginLoss(margin=0.5)
        
        # 优化器
        self.optimizer = optim.Adam([
            {'params': self.model.backbone.parameters(), 'lr': 1e-5},
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-3}
        ])
        
        # 学习率调度
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=2, gamma=0.5)
        
        logger.info(f"CPU训练器初始化完成: {max_cats} 只猫咪, {len(self.dataloader)} 批次/轮")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_feat = self.model(anchor)
            positive_feat = self.model(positive)
            negative_feat = self.model(negative)
            
            # 计算损失
            loss = self.criterion(anchor_feat, positive_feat, negative_feat)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 10 == 0:
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}")
        
        self.scheduler.step()
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def validate_on_real_task(self, save_path: str):
        """在真实任务上验证效果"""
        logger.info("🔍 开始真实任务验证...")
        
        # 保存当前模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'feature_dim': 512,
            'model_type': 'SimpleMegaDescriptor'
        }, save_path)
        
        # 创建识别器进行测试
        recognizer = create_adaptive_recognizer(save_path, device='cpu', strategy='balanced')
        
        # 随机选择测试猫咪
        test_cats = random.sample(self.dataset.cat_ids, min(10, len(self.dataset.cat_ids)))
        
        # 注册和测试
        registered_cats = []
        for cat_id in test_cats:
            images = self.dataset.cat_images[cat_id]
            train_count = max(3, int(len(images) * 0.7))
            train_images = images[:train_count]
            test_images = images[train_count:]
            
            result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
            if result['success'] and test_images:
                registered_cats.append((cat_id, test_images))
        
        # 识别测试
        correct = 0
        total = 0
        
        for cat_id, test_images in registered_cats:
            test_image = random.choice(test_images)
            result = recognizer.recognize_cat(test_image)
            
            total += 1
            if result.get('success') and result.get('cat_id') == cat_id:
                correct += 1
        
        accuracy = correct / total if total > 0 else 0.0
        
        logger.info(f"✅ 真实任务验证结果: {accuracy:.1%} ({correct}/{total})")
        
        return accuracy
    
    def train_with_validation(self, epochs: int = 3, save_path: str = 'cpu_megadescriptor.pth'):
        """边训练边验证"""
        logger.info(f"🚀 开始CPU训练: {epochs} epochs, {self.max_cats} 只猫咪")
        
        best_accuracy = 0.0
        training_history = []
        
        # 训练前基线测试
        logger.info("📊 训练前基线测试...")
        baseline_accuracy = self.validate_on_real_task(save_path)
        logger.info(f"基线准确率: {baseline_accuracy:.1%}")
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            avg_loss = self.train_epoch(epoch + 1)
            
            # 真实任务验证
            accuracy = self.validate_on_real_task(save_path)
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            training_history.append({
                'epoch': epoch + 1,
                'loss': avg_loss,
                'accuracy': accuracy,
                'time': epoch_time
            })
            
            logger.info(f"📊 Epoch {epoch + 1}/{epochs}:")
            logger.info(f"   损失: {avg_loss:.4f}")
            logger.info(f"   真实准确率: {accuracy:.1%}")
            logger.info(f"   vs基线: {accuracy - baseline_accuracy:+.1%}")
            logger.info(f"   用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if accuracy > best_accuracy:
                best_accuracy = accuracy
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'feature_dim': 512,
                    'model_type': 'SimpleMegaDescriptor',
                    'accuracy': accuracy
                }, save_path.replace('.pth', '_best.pth'))
                logger.info(f"🎉 保存最佳模型: 准确率 {best_accuracy:.1%}")
            
            # 提升检查
            improvement = accuracy - baseline_accuracy
            if improvement >= 0.05:
                logger.info(f"🎯 显著提升 {improvement:.1%}，训练有效!")
            elif improvement >= 0.02:
                logger.info(f"📈 适度提升 {improvement:.1%}，继续训练...")
            else:
                logger.info(f"⚠️ 提升有限 {improvement:.1%}，需要调整策略")
        
        final_improvement = best_accuracy - baseline_accuracy
        logger.info(f"🏁 训练完成!")
        logger.info(f"   基线准确率: {baseline_accuracy:.1%}")
        logger.info(f"   最佳准确率: {best_accuracy:.1%}")
        logger.info(f"   总体提升: {final_improvement:+.1%}")
        
        # 结论
        if final_improvement >= 0.05:
            conclusion = "✅ 训练有效，特征提升策略成功!"
        elif final_improvement >= 0.02:
            conclusion = "📈 训练有一定效果，可以继续优化"
        else:
            conclusion = "⚠️ 训练效果有限，需要重新考虑策略"
        
        logger.info(f"🎯 结论: {conclusion}")
        
        return training_history, best_accuracy, baseline_accuracy

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CPU快速训练验证')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=50,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=3,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='cpu_megadescriptor_50cats.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = CPUTrainer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    history, best_acc, baseline_acc = trainer.train_with_validation(
        epochs=args.epochs, 
        save_path=args.output
    )
    
    improvement = best_acc - baseline_acc
    logger.info(f"🎉 最终结果: 基线{baseline_acc:.1%} → 最佳{best_acc:.1%} (提升{improvement:+.1%})")

if __name__ == "__main__":
    main()
