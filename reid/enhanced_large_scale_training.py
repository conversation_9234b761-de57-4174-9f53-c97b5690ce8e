#!/usr/bin/env python3
"""
增强大规模训练 - 提升特征区分能力的终极方案
1. 扩大数据集规模 (150-200只猫咪)
2. 提升特征维度 (1024维)
3. 困难样本挖掘
4. 多尺度特征融合
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltimateTripletDataset(Dataset):
    """终极三元组数据集 - 专注于困难样本"""
    
    def __init__(self, dataset_path: str, max_cats: int = 200):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        
        # 极强的数据增强
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomCrop((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=45),
            transforms.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.3),
            transforms.RandomAffine(degrees=0, translate=(0.3, 0.3), scale=(0.7, 1.3)),
            transforms.RandomPerspective(distortion_scale=0.2, p=0.5),
            transforms.RandomGrayscale(p=0.15),
            transforms.GaussianBlur(kernel_size=5, sigma=(0.1, 3.0)),
            transforms.RandomErasing(p=0.2, scale=(0.02, 0.15)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.cat_images = defaultdict(list)
        self.cat_ids = []
        self._build_dataset()
        
        logger.info(f"终极数据集构建完成: {len(self.cat_ids)} 只猫咪")
    
    def _build_dataset(self):
        """构建大规模数据集"""
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                valid_cats.append((cat_folder.name, images))
        
        # 按图片数量排序
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        selected_cats = valid_cats[:self.max_cats]
        
        for cat_id, images in selected_cats:
            self.cat_images[cat_id] = [str(img) for img in images]
            self.cat_ids.append(cat_id)
        
        logger.info(f"选择了 {len(selected_cats)} 只猫咪，总图片: {sum(len(imgs) for imgs in self.cat_images.values())}")
    
    def __len__(self):
        return len(self.cat_ids) * 300  # 增加训练样本数
    
    def __getitem__(self, idx):
        """生成困难三元组"""
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择anchor和positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择困难负样本 (ID相近的猫咪)
        anchor_id_num = int(anchor_cat)
        candidate_negatives = []
        
        for cat_id in self.cat_ids:
            if cat_id != anchor_cat:
                id_diff = abs(int(cat_id) - anchor_id_num)
                if id_diff < 100:  # ID相近的猫咪
                    candidate_negatives.append(cat_id)
        
        if candidate_negatives and random.random() < 0.8:
            negative_cat = random.choice(candidate_negatives)
        else:
            negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative
        except Exception as e:
            return self.__getitem__(random.randint(0, len(self) - 1))

class UltimateMegaDescriptor(nn.Module):
    """终极MegaDescriptor - 1024维高质量特征"""
    
    def __init__(self, feature_dim=1024):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 多层特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一阶段：扩展
            nn.Linear(backbone_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.GELU(),
            nn.Dropout(0.2),
            
            # 第二阶段：深度处理
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.15),
            
            # 第三阶段：精炼
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            
            # 第四阶段：最终优化
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.05),
            
            # 输出层
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 多头自注意力
        self.self_attention = nn.MultiheadAttention(
            feature_dim, num_heads=32, dropout=0.1, batch_first=True
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(feature_dim)
        
        logger.info(f"终极MegaDescriptor初始化: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        # 骨干网络特征
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 自注意力机制
        enhanced_unsqueezed = enhanced_features.unsqueeze(1)
        attended, _ = self.self_attention(
            enhanced_unsqueezed, enhanced_unsqueezed, enhanced_unsqueezed
        )
        attended = attended.squeeze(1)
        
        # 残差连接和层归一化
        residual_features = self.layer_norm(enhanced_features + attended)
        
        # L2归一化
        return torch.nn.functional.normalize(residual_features, p=2, dim=1)

class FocalTripletLoss(nn.Module):
    """焦点三元组损失 - 专注困难样本"""
    
    def __init__(self, margin=0.5, alpha=2.0, gamma=2.0):
        super().__init__()
        self.margin = margin
        self.alpha = alpha
        self.gamma = gamma
    
    def forward(self, anchor, positive, negative):
        pos_dist = torch.norm(anchor - positive, p=2, dim=1)
        neg_dist = torch.norm(anchor - negative, p=2, dim=1)
        
        # 基础损失
        basic_loss = torch.clamp(pos_dist - neg_dist + self.margin, min=0.0)
        
        # 焦点权重
        focal_weight = torch.pow(1 - torch.exp(-basic_loss), self.gamma)
        
        # 困难样本权重
        hard_weight = torch.pow(pos_dist / (neg_dist + 1e-8), self.alpha)
        
        # 最终损失
        final_loss = focal_weight * hard_weight * basic_loss
        
        return final_loss.mean()

class UltimateTrainer:
    """终极训练器"""
    
    def __init__(self, dataset_path: str, max_cats: int = 200):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 数据集和数据加载器
        self.dataset = UltimateTripletDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=12,  # 根据GPU内存调整
            shuffle=True, 
            num_workers=6,
            pin_memory=True,
            persistent_workers=True
        )
        
        # 模型
        self.model = UltimateMegaDescriptor(feature_dim=1024).to(self.device)
        
        # 损失函数
        self.criterion = FocalTripletLoss(margin=0.5, alpha=2.0, gamma=2.0)
        
        # 优化器 - 分层学习率
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 5e-6},
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-3},
            {'params': self.model.self_attention.parameters(), 'lr': 5e-4},
            {'params': self.model.layer_norm.parameters(), 'lr': 1e-4}
        ], weight_decay=1e-4)
        
        # 学习率调度
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer,
            max_lr=[5e-6, 1e-3, 5e-4, 1e-4],
            epochs=25,
            steps_per_epoch=len(self.dataloader),
            pct_start=0.1,
            anneal_strategy='cos'
        )
        
        logger.info(f"终极训练器初始化完成: {max_cats} 只猫咪")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_feat = self.model(anchor)
            positive_feat = self.model(positive)
            negative_feat = self.model(negative)
            
            # 计算损失
            loss = self.criterion(anchor_feat, positive_feat, negative_feat)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            self.scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 50 == 0:
                lr = self.scheduler.get_last_lr()[1]
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: Loss={loss.item():.4f}, LR={lr:.2e}")
        
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def evaluate_separability(self):
        """评估特征分离度"""
        self.model.eval()
        
        eval_cats = random.sample(self.dataset.cat_ids, min(50, len(self.dataset.cat_ids)))
        intra_dists = []
        inter_dists = []
        
        with torch.no_grad():
            for cat_id in eval_cats:
                images = self.dataset.cat_images[cat_id]
                
                # 类内距离
                if len(images) >= 2:
                    img1, img2 = random.sample(images, 2)
                    
                    tensor1 = self.dataset.transform(Image.open(img1).convert('RGB')).unsqueeze(0).to(self.device)
                    tensor2 = self.dataset.transform(Image.open(img2).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1 = self.model(tensor1)
                    feat2 = self.model(tensor2)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_dists.append(intra_dist)
                
                # 类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img = random.choice(self.dataset.cat_images[other_cat])
                
                tensor1 = self.dataset.transform(Image.open(random.choice(images)).convert('RGB')).unsqueeze(0).to(self.device)
                tensor2 = self.dataset.transform(Image.open(other_img).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1 = self.model(tensor1)
                feat2 = self.model(tensor2)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_dists.append(inter_dist)
        
        avg_intra = np.mean(intra_dists) if intra_dists else 0.0
        avg_inter = np.mean(inter_dists) if inter_dists else 0.0
        separability = avg_inter / (avg_intra + 1e-8)
        
        return {
            'intra_distance': avg_intra,
            'inter_distance': avg_inter,
            'separability': separability
        }
    
    def train(self, epochs: int = 25, save_path: str = 'ultimate_megadescriptor.pth'):
        """完整训练流程"""
        logger.info(f"开始终极训练: {epochs} epochs, {self.max_cats} 只猫咪")
        
        best_separability = 0.0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            avg_loss = self.train_epoch(epoch + 1)
            
            # 评估
            eval_stats = self.evaluate_separability()
            
            epoch_time = time.time() - start_time
            
            logger.info(f"Epoch {epoch + 1}/{epochs}:")
            logger.info(f"  损失: {avg_loss:.4f}")
            logger.info(f"  分离度: {eval_stats['separability']:.4f}")
            logger.info(f"  类内距离: {eval_stats['intra_distance']:.4f}")
            logger.info(f"  类间距离: {eval_stats['inter_distance']:.4f}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if eval_stats['separability'] > best_separability:
                best_separability = eval_stats['separability']
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 分离度 {best_separability:.4f}")
        
        logger.info(f"🚀 终极训练完成! 最佳分离度: {best_separability:.4f}")
        return best_separability
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'cat_ids': self.dataset.cat_ids,
            'num_cats': len(self.dataset.cat_ids),
            'feature_dim': 1024,
            'model_type': 'UltimateMegaDescriptor'
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='终极大规模训练')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=200,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=20,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='ultimate_megadescriptor_200cats.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = UltimateTrainer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    best_sep = trainer.train(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"🎉 终极训练完成! 最佳分离度: {best_sep:.4f}")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
