#!/usr/bin/env python3
"""
增强模型测试脚本 - 验证特征增强训练的效果
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_recognizer import create_enhanced_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_enhanced_test(dataset_path: str, model_path: str = None, 
                     cat_counts: List[int] = [3, 5, 10], rounds: int = 3):
    """运行增强模型测试"""
    dataset_path = Path(dataset_path)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    model_name = "增强模型" if model_path else "基础模型"
    print(f"🚀 {model_name}测试")
    print("=" * 60)
    print(f"可用猫咪数量: {len(available_cats)}")
    if model_path:
        print(f"模型路径: {model_path}")
    
    results = {}
    
    for cat_count in cat_counts:
        if cat_count > len(available_cats):
            print(f"⚠️ 跳过 {cat_count} 只猫咪测试：可用数据不足")
            continue
        
        print(f"\n🎯 测试 {cat_count} 只猫咪")
        print("-" * 40)
        
        round_results = []
        
        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮测试...")
            
            # 创建识别器 - 使用CPU避免GPU内存冲突
            recognizer = create_enhanced_recognizer(model_path, device='cpu')
            
            # 随机选择猫咪
            selected_cats = random.sample(available_cats, cat_count)
            
            # 注册猫咪
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            response_times = []
            similarities = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                
                start_time = time.time()
                result = recognizer.recognize_cat(test_image)
                response_time = time.time() - start_time
                
                response_times.append(response_time)
                total += 1
                
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                if is_correct:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                # 记录相似度
                if 'similarity' in result:
                    similarities.append(result['similarity'])
                elif 'best_match' in result and 'similarity' in result['best_match']:
                    similarities.append(result['best_match']['similarity'])
                
                # 显示详细结果
                status = "✅" if is_correct else "❌"
                confidence = result.get('confidence', 0.0)
                similarity = result.get('similarity', result.get('best_match', {}).get('similarity', 0.0))
                threshold = result.get('threshold_used', 0.0)
                
                print(f"    {status} {cat_id}: 置信度={confidence:.1%}, 相似度={similarity:.3f}, 阈值={threshold:.3f}")
            
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_response_time = np.mean(response_times) if response_times else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            
            round_results.append({
                'accuracy': accuracy,
                'confidence': avg_confidence,
                'response_time': avg_response_time,
                'similarity': avg_similarity,
                'correct': correct,
                'total': total
            })
            
            print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, 平均相似度: {avg_similarity:.3f}")
        
        # 计算统计
        accuracies = [r['accuracy'] for r in round_results]
        similarities_avg = [r['similarity'] for r in round_results]
        
        avg_accuracy = np.mean(accuracies)
        accuracy_std = np.std(accuracies)
        avg_similarity_overall = np.mean(similarities_avg)
        
        results[cat_count] = {
            'avg_accuracy': avg_accuracy,
            'accuracy_std': accuracy_std,
            'avg_similarity': avg_similarity_overall,
            'rounds': round_results,
            'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
            'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95)
        }
        
        print(f"✅ 平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
        print(f"   平均相似度: {avg_similarity_overall:.3f}")
        print(f"   高准确率轮次: {results[cat_count]['high_accuracy_rounds']}/{rounds}")
    
    return results

def compare_models(dataset_path: str, enhanced_model_path: str, 
                  cat_counts: List[int] = [3, 5, 10], rounds: int = 3):
    """对比基础模型和增强模型"""
    print("🔬 模型对比测试")
    print("=" * 80)
    
    # 测试基础模型
    print("\n📊 基础模型测试")
    baseline_results = run_enhanced_test(dataset_path, None, cat_counts, rounds)
    
    # 测试增强模型
    print("\n📊 增强模型测试")
    enhanced_results = run_enhanced_test(dataset_path, enhanced_model_path, cat_counts, rounds)
    
    # 对比结果
    print("\n" + "=" * 80)
    print("📈 模型对比结果")
    print("=" * 80)
    
    print(f"{'规模':<6} {'基础模型':<12} {'增强模型':<12} {'提升幅度':<10} {'相似度提升':<12}")
    print("-" * 70)
    
    for cat_count in cat_counts:
        if cat_count in baseline_results and cat_count in enhanced_results:
            baseline_acc = baseline_results[cat_count]['avg_accuracy']
            enhanced_acc = enhanced_results[cat_count]['avg_accuracy']
            improvement = enhanced_acc - baseline_acc
            
            baseline_sim = baseline_results[cat_count]['avg_similarity']
            enhanced_sim = enhanced_results[cat_count]['avg_similarity']
            sim_improvement = enhanced_sim - baseline_sim
            
            print(f"{cat_count:<6} {baseline_acc:<12.1%} {enhanced_acc:<12.1%} "
                  f"{improvement:<10.1%} {sim_improvement:<12.3f}")
    
    return {
        'baseline': baseline_results,
        'enhanced': enhanced_results
    }

def display_summary(results: Dict, model_name: str = "增强模型"):
    """显示测试总结"""
    print(f"\n📊 {model_name}测试总结")
    print("=" * 60)
    
    if not results:
        print("没有测试结果")
        return
    
    print(f"{'规模':<8} {'平均准确率':<12} {'稳定性':<10} {'平均相似度':<12} {'高准确率轮次':<12}")
    print("-" * 70)
    
    for scale in sorted(results.keys()):
        result = results[scale]
        stability = 1.0 - result['accuracy_std']
        high_acc_ratio = result['high_accuracy_rounds'] / len(result['rounds'])
        
        print(f"{scale:<8} {result['avg_accuracy']:<12.1%} {stability:<10.1%} "
              f"{result['avg_similarity']:<12.3f} {high_acc_ratio:<12.1%}")
    
    # 系统评价
    min_accuracy = min(results[scale]['avg_accuracy'] for scale in results)
    max_scale = max(results.keys())
    
    print(f"\n🎯 系统评价:")
    print(f"   最大测试规模: {max_scale} 只猫咪")
    print(f"   最低准确率: {min_accuracy:.1%}")
    
    if min_accuracy >= 0.95:
        rating = "🌟 优秀 - 达到95%+目标"
    elif min_accuracy >= 0.9:
        rating = "✅ 良好 - 接近目标"
    elif min_accuracy >= 0.8:
        rating = "⚠️ 一般 - 需要进一步优化"
    else:
        rating = "❌ 需要改进 - 特征提取能力不足"
    
    print(f"   总体评价: {rating}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='增强模型测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, default=None,
                       help='增强模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    parser.add_argument('--compare', action='store_true',
                       help='对比基础模型和增强模型')
    parser.add_argument('--output', type=str, default='enhanced_test_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    if args.compare and args.model:
        # 对比测试
        results = compare_models(args.dataset, args.model, args.scales, args.rounds)
    else:
        # 单独测试
        results = run_enhanced_test(args.dataset, args.model, args.scales, args.rounds)
        model_name = "增强模型" if args.model else "基础模型"
        display_summary(results, model_name)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
