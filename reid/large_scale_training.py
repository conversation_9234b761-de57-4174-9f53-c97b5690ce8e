#!/usr/bin/env python3
"""
大规模数据续训脚本 - 使用完整数据集提升模型泛化能力
支持任意规模的猫咪识别训练
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import json
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from universal_cat_recognizer import UniversalFeatureExtractor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LargeScaleCatDataset(Dataset):
    """大规模猫咪数据集"""
    
    def __init__(self, dataset_path: str, max_cats: int = None, min_images_per_cat: int = 5):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        self.min_images_per_cat = min_images_per_cat
        
        # 数据增强 - 使用384x384以匹配MegaDescriptor-L-384
        self.transform = transforms.Compose([
            transforms.Resize((384, 384)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.samples = []
        self.cat_to_idx = {}
        self.idx_to_cat = {}
        
        self._build_dataset()
        
        logger.info(f"数据集构建完成: {len(self.samples)} 个样本, {len(self.cat_to_idx)} 只猫咪")
    
    def _build_dataset(self):
        """构建数据集"""
        # 获取所有猫咪文件夹
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        # 过滤图片数量不足的猫咪
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= self.min_images_per_cat:
                valid_cats.append((cat_folder, images))
        
        # 按图片数量排序，优先选择数据丰富的猫咪
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        
        # 限制猫咪数量
        if self.max_cats:
            valid_cats = valid_cats[:self.max_cats]
        
        # 构建样本列表
        cat_idx = 0
        for cat_folder, images in valid_cats:
            cat_id = cat_folder.name
            self.cat_to_idx[cat_id] = cat_idx
            self.idx_to_cat[cat_idx] = cat_id
            
            for img_path in images:
                self.samples.append((str(img_path), cat_idx))
            
            cat_idx += 1
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        img_path, cat_idx = self.samples[idx]
        
        try:
            # 加载图像
            image = Image.open(img_path).convert('RGB')
            image = self.transform(image)
            
            return image, cat_idx
        except Exception as e:
            logger.warning(f"加载图片失败 {img_path}: {e}")
            # 返回随机样本作为备用
            return self.__getitem__(random.randint(0, len(self.samples) - 1))

class ContrastiveLoss(nn.Module):
    """对比学习损失"""
    
    def __init__(self, temperature=0.07, margin=0.5):
        super().__init__()
        self.temperature = temperature
        self.margin = margin
    
    def forward(self, features, labels):
        """计算对比学习损失"""
        batch_size = features.shape[0]
        
        # 归一化特征
        features = F.normalize(features, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # 创建标签掩码
        labels = labels.view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(features.device)
        
        # 正样本掩码 (排除自己)
        positive_mask = mask * (1 - torch.eye(batch_size, device=features.device))
        
        # 负样本掩码
        negative_mask = 1 - mask
        
        # 计算正样本损失
        positive_logits = similarity_matrix * positive_mask
        positive_logits = positive_logits.sum(dim=1) / (positive_mask.sum(dim=1) + 1e-8)
        
        # 计算负样本损失
        negative_logits = similarity_matrix * negative_mask
        negative_logits = torch.logsumexp(negative_logits, dim=1)
        
        # 总损失
        loss = -positive_logits + negative_logits
        
        return loss.mean()

class LargeScaleTrainer:
    """大规模训练器"""
    
    def __init__(self, dataset_path: str, max_cats: int = 200, device='cuda'):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 创建数据集
        self.dataset = LargeScaleCatDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=32, 
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        
        # 创建模型
        self.model = UniversalFeatureExtractor().to(self.device)
        
        # 损失函数
        self.contrastive_loss = ContrastiveLoss().to(self.device)
        self.classification_loss = nn.CrossEntropyLoss()
        
        # 分类头（用于辅助训练）
        self.classifier = nn.Linear(1024, len(self.dataset.cat_to_idx)).to(self.device)
        
        # 优化器
        self.optimizer = optim.AdamW([
            {'params': self.model.parameters(), 'lr': 1e-4},
            {'params': self.classifier.parameters(), 'lr': 1e-3}
        ], weight_decay=1e-5)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=50, eta_min=1e-6
        )
        
        logger.info(f"训练器初始化完成: {len(self.dataset.cat_to_idx)} 只猫咪, {len(self.dataset)} 个样本")
    
    def train_epoch(self, epoch: int) -> Dict:
        """训练一个epoch"""
        self.model.train()
        self.classifier.train()
        
        total_loss = 0.0
        contrastive_loss_sum = 0.0
        classification_loss_sum = 0.0
        correct_predictions = 0
        total_predictions = 0
        
        for batch_idx, (images, labels) in enumerate(self.dataloader):
            images = images.to(self.device)
            labels = labels.to(self.device)
            
            # 前向传播
            feature_dict = self.model(images)
            enhanced_features = feature_dict['enhanced_features']
            contrastive_features = feature_dict['contrastive_features']
            
            # 分类预测
            logits = self.classifier(enhanced_features)
            
            # 计算损失
            contrastive_loss = self.contrastive_loss(contrastive_features, labels)
            classification_loss = self.classification_loss(logits, labels)
            
            # 总损失 (对比学习为主，分类为辅)
            total_batch_loss = 0.7 * contrastive_loss + 0.3 * classification_loss
            
            # 反向传播
            self.optimizer.zero_grad()
            total_batch_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            torch.nn.utils.clip_grad_norm_(self.classifier.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # 统计
            total_loss += total_batch_loss.item()
            contrastive_loss_sum += contrastive_loss.item()
            classification_loss_sum += classification_loss.item()
            
            # 计算准确率
            _, predicted = torch.max(logits, 1)
            correct_predictions += (predicted == labels).sum().item()
            total_predictions += labels.size(0)
            
            # 打印进度
            if batch_idx % 50 == 0:
                logger.info(f"Epoch {epoch}, Batch {batch_idx}/{len(self.dataloader)}: "
                          f"Loss={total_batch_loss.item():.4f}, "
                          f"Acc={correct_predictions/total_predictions:.3f}")
        
        # 更新学习率
        self.scheduler.step()
        
        return {
            'total_loss': total_loss / len(self.dataloader),
            'contrastive_loss': contrastive_loss_sum / len(self.dataloader),
            'classification_loss': classification_loss_sum / len(self.dataloader),
            'accuracy': correct_predictions / total_predictions,
            'learning_rate': self.scheduler.get_last_lr()[0]
        }
    
    def train(self, epochs: int = 20, save_path: str = 'universal_model.pth') -> Dict:
        """完整训练流程"""
        logger.info(f"开始大规模训练: {epochs} epochs")
        
        training_history = {
            'epochs': [],
            'losses': [],
            'accuracies': [],
            'contrastive_losses': [],
            'classification_losses': []
        }
        
        best_accuracy = 0.0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练一个epoch
            epoch_stats = self.train_epoch(epoch + 1)
            
            # 记录历史
            training_history['epochs'].append(epoch + 1)
            training_history['losses'].append(epoch_stats['total_loss'])
            training_history['accuracies'].append(epoch_stats['accuracy'])
            training_history['contrastive_losses'].append(epoch_stats['contrastive_loss'])
            training_history['classification_losses'].append(epoch_stats['classification_loss'])
            
            epoch_time = time.time() - start_time
            
            logger.info(f"Epoch {epoch + 1}/{epochs} 完成:")
            logger.info(f"  总损失: {epoch_stats['total_loss']:.4f}")
            logger.info(f"  对比损失: {epoch_stats['contrastive_loss']:.4f}")
            logger.info(f"  分类损失: {epoch_stats['classification_loss']:.4f}")
            logger.info(f"  准确率: {epoch_stats['accuracy']:.3f}")
            logger.info(f"  学习率: {epoch_stats['learning_rate']:.2e}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if epoch_stats['accuracy'] > best_accuracy:
                best_accuracy = epoch_stats['accuracy']
                self.save_model(save_path)
                logger.info(f"保存最佳模型: 准确率 {best_accuracy:.3f}")
        
        logger.info(f"训练完成! 最佳准确率: {best_accuracy:.3f}")
        
        return training_history
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'classifier_state_dict': self.classifier.state_dict(),
            'cat_to_idx': self.dataset.cat_to_idx,
            'idx_to_cat': self.dataset.idx_to_cat,
            'num_cats': len(self.dataset.cat_to_idx)
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='大规模猫咪识别训练')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=200,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=20,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='universal_model.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = LargeScaleTrainer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    history = trainer.train(epochs=args.epochs, save_path=args.output)
    
    # 保存训练历史
    history_path = args.output.replace('.pth', '_history.json')
    with open(history_path, 'w') as f:
        json.dump(history, f, indent=2)
    
    logger.info(f"训练历史已保存: {history_path}")

if __name__ == "__main__":
    main()
