#!/usr/bin/env python3
"""
性能基准测试 - 建立不同规模下的性能基准
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from production_recognizer import create_production_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BenchmarkTester:
    """基准测试器"""
    
    def __init__(self, dataset_path: str, model_path: str = None):
        self.dataset_path = Path(dataset_path)
        self.model_path = model_path
        
        # 获取可用猫咪数据
        self.available_cats = self._load_cat_data()
        
        logger.info(f"基准测试器初始化完成: {len(self.available_cats)} 只可用猫咪")
    
    def _load_cat_data(self) -> List[Tuple[str, List[str]]]:
        """加载猫咪数据"""
        cats = []
        for cat_folder in self.dataset_path.iterdir():
            if not cat_folder.is_dir() or not cat_folder.name.isdigit():
                continue
            
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                cats.append((cat_folder.name, [str(img) for img in images]))
        
        # 按图片数量排序
        cats.sort(key=lambda x: len(x[1]), reverse=True)
        return cats
    
    def run_scale_benchmark(self, scales: List[int], rounds: int = 5) -> Dict:
        """运行规模基准测试"""
        results = {}
        
        print("🚀 规模基准测试")
        print("=" * 60)
        print(f"模型: {'增强模型' if self.model_path else '基础模型'}")
        print(f"测试规模: {scales}")
        print(f"每个规模轮数: {rounds}")
        
        for scale in scales:
            if scale > len(self.available_cats):
                print(f"⚠️ 跳过规模 {scale}: 可用数据不足")
                continue
            
            print(f"\n🎯 测试规模: {scale} 只猫咪")
            print("-" * 40)
            
            scale_results = []
            
            for round_num in range(rounds):
                print(f"第 {round_num + 1} 轮...")
                
                # 创建识别器
                recognizer = create_production_recognizer(
                    self.model_path, 
                    device='cpu',  # 使用CPU避免内存冲突
                    batch_size=4
                )
                
                # 随机选择猫咪
                selected_cats = random.sample(self.available_cats, scale)
                
                # 准备注册数据
                registration_data = []
                test_data = []
                
                for cat_id, image_paths in selected_cats:
                    train_count = max(3, int(len(image_paths) * 0.7))
                    train_images = image_paths[:train_count]
                    test_images = image_paths[train_count:]
                    
                    if test_images:
                        registration_data.append((cat_id, f"Cat_{cat_id}", train_images))
                        test_data.append((cat_id, random.choice(test_images)))
                
                # 批量注册
                reg_start = time.time()
                reg_results = recognizer.register_cat_batch(registration_data)
                reg_time = time.time() - reg_start
                
                successful_registrations = sum(1 for r in reg_results if r['success'])
                
                # 批量识别
                test_images = [img_path for _, img_path in test_data]
                
                rec_start = time.time()
                rec_results = recognizer.recognize_cat_batch(test_images)
                rec_time = time.time() - rec_start
                
                # 计算准确率
                correct = 0
                confidences = []
                similarities = []
                
                for i, (expected_cat_id, _) in enumerate(test_data):
                    result = rec_results[i]
                    if result.success and result.cat_id == expected_cat_id:
                        correct += 1
                        confidences.append(result.confidence)
                    
                    similarities.append(result.similarity)
                
                accuracy = correct / len(test_data) if test_data else 0.0
                avg_confidence = np.mean(confidences) if confidences else 0.0
                avg_similarity = np.mean(similarities) if similarities else 0.0
                
                # 获取性能报告
                perf_report = recognizer.get_performance_report()
                
                round_result = {
                    'accuracy': accuracy,
                    'avg_confidence': avg_confidence,
                    'avg_similarity': avg_similarity,
                    'registration_time': reg_time,
                    'recognition_time': rec_time,
                    'total_time': reg_time + rec_time,
                    'successful_registrations': successful_registrations,
                    'total_tests': len(test_data),
                    'correct_recognitions': correct,
                    'throughput': perf_report['throughput'],
                    'avg_response_time': perf_report['avg_response_time']
                }
                
                scale_results.append(round_result)
                
                print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, "
                      f"相似度: {avg_similarity:.3f}, 总用时: {reg_time + rec_time:.1f}s")
            
            # 计算统计
            accuracies = [r['accuracy'] for r in scale_results]
            avg_accuracy = np.mean(accuracies)
            accuracy_std = np.std(accuracies)
            
            results[scale] = {
                'avg_accuracy': avg_accuracy,
                'accuracy_std': accuracy_std,
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'avg_confidence': np.mean([r['avg_confidence'] for r in scale_results]),
                'avg_similarity': np.mean([r['avg_similarity'] for r in scale_results]),
                'avg_registration_time': np.mean([r['registration_time'] for r in scale_results]),
                'avg_recognition_time': np.mean([r['recognition_time'] for r in scale_results]),
                'avg_total_time': np.mean([r['total_time'] for r in scale_results]),
                'avg_throughput': np.mean([r['throughput'] for r in scale_results]),
                'rounds': scale_results,
                'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95),
                'good_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.9)
            }
            
            print(f"✅ 规模 {scale}: 平均准确率 {avg_accuracy:.1%} ± {accuracy_std:.1%}")
            print(f"   高准确率轮次: {results[scale]['high_accuracy_rounds']}/{rounds}")
            print(f"   良好准确率轮次: {results[scale]['good_accuracy_rounds']}/{rounds}")
        
        return results
    
    def generate_benchmark_report(self, results: Dict) -> Dict:
        """生成基准报告"""
        report = {
            'test_info': {
                'model_type': '增强模型' if self.model_path else '基础模型',
                'model_path': self.model_path,
                'dataset_path': str(self.dataset_path),
                'available_cats': len(self.available_cats),
                'test_time': datetime.now().isoformat()
            },
            'performance_summary': {},
            'scale_analysis': {},
            'recommendations': []
        }
        
        if not results:
            return report
        
        # 性能总结
        all_accuracies = []
        all_scales = sorted(results.keys())
        
        for scale in all_scales:
            all_accuracies.append(results[scale]['avg_accuracy'])
        
        report['performance_summary'] = {
            'tested_scales': all_scales,
            'overall_avg_accuracy': np.mean(all_accuracies),
            'accuracy_decline_rate': (all_accuracies[0] - all_accuracies[-1]) / len(all_scales) if len(all_accuracies) > 1 else 0,
            'min_accuracy': min(all_accuracies),
            'max_accuracy': max(all_accuracies),
            'scalability_score': min(all_accuracies) / max(all_accuracies) if max(all_accuracies) > 0 else 0
        }
        
        # 规模分析
        for scale in all_scales:
            result = results[scale]
            report['scale_analysis'][scale] = {
                'performance_rating': self._rate_performance(result['avg_accuracy']),
                'stability_rating': self._rate_stability(result['accuracy_std']),
                'efficiency_rating': self._rate_efficiency(result['avg_total_time'], scale),
                'key_metrics': {
                    'accuracy': result['avg_accuracy'],
                    'stability': 1.0 - result['accuracy_std'],
                    'avg_time': result['avg_total_time'],
                    'throughput': result['avg_throughput']
                }
            }
        
        # 生成建议
        min_accuracy = report['performance_summary']['min_accuracy']
        scalability_score = report['performance_summary']['scalability_score']
        
        if min_accuracy >= 0.95:
            report['recommendations'].append("🌟 系统表现优秀，已达到95%+目标")
        elif min_accuracy >= 0.9:
            report['recommendations'].append("✅ 系统表现良好，接近目标，建议微调优化")
        elif min_accuracy >= 0.8:
            report['recommendations'].append("⚠️ 系统表现一般，建议进一步训练优化")
        else:
            report['recommendations'].append("❌ 系统需要重大改进，建议重新训练")
        
        if scalability_score < 0.8:
            report['recommendations'].append("📈 建议改进大规模场景下的性能")
        
        return report
    
    def _rate_performance(self, accuracy: float) -> str:
        """评价性能"""
        if accuracy >= 0.95:
            return "优秀"
        elif accuracy >= 0.9:
            return "良好"
        elif accuracy >= 0.8:
            return "一般"
        else:
            return "需改进"
    
    def _rate_stability(self, std: float) -> str:
        """评价稳定性"""
        if std <= 0.05:
            return "优秀"
        elif std <= 0.1:
            return "良好"
        elif std <= 0.15:
            return "一般"
        else:
            return "不稳定"
    
    def _rate_efficiency(self, avg_time: float, scale: int) -> str:
        """评价效率"""
        time_per_cat = avg_time / scale
        if time_per_cat <= 1.0:
            return "优秀"
        elif time_per_cat <= 2.0:
            return "良好"
        elif time_per_cat <= 3.0:
            return "一般"
        else:
            return "需优化"

def display_benchmark_results(results: Dict, report: Dict):
    """显示基准测试结果"""
    print("\n" + "=" * 80)
    print("📊 基准测试结果")
    print("=" * 80)
    
    # 基本信息
    info = report['test_info']
    print(f"模型类型: {info['model_type']}")
    print(f"可用猫咪: {info['available_cats']} 只")
    
    # 性能总结
    summary = report['performance_summary']
    print(f"\n🎯 性能总结:")
    print(f"   测试规模: {summary['tested_scales']}")
    print(f"   整体平均准确率: {summary['overall_avg_accuracy']:.1%}")
    print(f"   最低准确率: {summary['min_accuracy']:.1%}")
    print(f"   最高准确率: {summary['max_accuracy']:.1%}")
    print(f"   可扩展性评分: {summary['scalability_score']:.1%}")
    
    # 详细结果
    print(f"\n📈 详细结果:")
    print(f"{'规模':<6} {'准确率':<10} {'稳定性':<8} {'置信度':<8} {'相似度':<8} {'用时':<8} {'评级':<8}")
    print("-" * 70)
    
    for scale in sorted(results.keys()):
        result = results[scale]
        analysis = report['scale_analysis'][scale]
        
        print(f"{scale:<6} {result['avg_accuracy']:<10.1%} "
              f"{1.0 - result['accuracy_std']:<8.1%} "
              f"{result['avg_confidence']:<8.1%} "
              f"{result['avg_similarity']:<8.3f} "
              f"{result['avg_total_time']:<8.1f} "
              f"{analysis['performance_rating']:<8}")
    
    # 建议
    print(f"\n💡 建议:")
    for rec in report['recommendations']:
        print(f"   {rec}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基准测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, default=None,
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10, 15, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='benchmark_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = BenchmarkTester(args.dataset, args.model)
    
    # 运行基准测试
    results = tester.run_scale_benchmark(args.scales, args.rounds)
    
    # 生成报告
    report = tester.generate_benchmark_report(results)
    
    # 显示结果
    display_benchmark_results(results, report)
    
    # 保存结果
    output_data = {
        'results': results,
        'report': report
    }
    
    with open(args.output, 'w') as f:
        json.dump(output_data, f, indent=2, default=str)
    
    print(f"\n💾 基准测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
