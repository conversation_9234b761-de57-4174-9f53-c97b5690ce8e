#!/usr/bin/env python3
"""
GPU内存优化版本的wildlife-tools训练
专门针对有限GPU内存设计，保持精度不变
"""

import os
import sys
import gc
import pandas as pd
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
import itertools
from typing import Dict, List, Tuple

import torch
import torch.optim as optim
import torchvision.transforms as T
import timm

# wildlife-tools imports
from wildlife_tools.data import ImageDataset
from wildlife_tools.train import BasicTrainer, ArcFaceLoss, TripletLoss, set_seed
from wildlife_tools.features import DeepFeatures
from wildlife_tools.similarity import CosineSimilarity
from wildlife_tools.inference import KnnClassifier

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def optimize_gpu_memory():
    """GPU内存优化设置"""
    if torch.cuda.is_available():
        # 清理GPU缓存
        torch.cuda.empty_cache()
        gc.collect()
        
        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
        
        # 启用cudnn优化
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # 显示GPU信息
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        logger.info(f"GPU: {gpu_name}")
        logger.info(f"GPU总内存: {gpu_memory:.1f}GB")
        
        return 'cuda'
    else:
        logger.warning("CUDA不可用，使用CPU")
        return 'cpu'

class MemoryEfficientImageDataset(ImageDataset):
    """内存高效的图像数据集"""
    
    def __init__(self, df, root, transform=None, cache_size=1000):
        super().__init__(df, root, transform)
        self.cache_size = cache_size
        self.image_cache = {}
        self.cache_order = []
    
    def __getitem__(self, idx):
        """内存优化的数据加载"""
        # 如果缓存过大，清理最旧的项目
        if len(self.image_cache) > self.cache_size:
            oldest_key = self.cache_order.pop(0)
            del self.image_cache[oldest_key]
        
        return super().__getitem__(idx)

class GPUOptimizedTrainer:
    """GPU内存优化的训练器"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        # GPU内存优化
        self.device = optimize_gpu_memory()
        
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            self.split_info = json.load(f)
        
        # 创建数据集
        self.train_dataset = self._create_train_dataset()
        self.test_dataset = self._create_test_dataset()
        
        # 创建模型
        self.backbone = self._create_backbone()
        
        # 创建损失函数
        self.objective = self._create_objective()
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        self.scheduler = self._create_scheduler()
        
        logger.info(f"GPU优化训练器初始化完成")
        logger.info(f"  设备: {self.device}")
        logger.info(f"  训练集大小: {len(self.train_dataset)}")
        logger.info(f"  类别数: {self.train_dataset.num_classes}")
    
    def _create_train_dataset(self):
        """创建训练数据集"""
        # 创建DataFrame
        rows = []
        for cat in self.split_info['train_data']:
            cat_id = cat['cat_id']
            images = cat['images']
            
            for img_path in images:
                relative_path = os.path.relpath(img_path, '../dataset/cat_individual_images')
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        df = pd.DataFrame(rows)
        root_path = '../dataset/cat_individual_images'
        
        # 内存优化的数据增强
        transform = T.Compose([
            T.Resize([224, 224]),  # 直接resize到目标尺寸
            T.RandomHorizontalFlip(p=0.5),
            T.RandomRotation(degrees=10),  # 减少旋转角度
            T.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1, hue=0.05),  # 减少增强强度
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 使用内存高效的数据集
        dataset = MemoryEfficientImageDataset(df, root_path, transform=transform, cache_size=500)
        
        logger.info(f"训练数据集创建完成: {len(dataset)} 张图片, {dataset.num_classes} 个类别")
        return dataset
    
    def _create_test_dataset(self):
        """创建测试数据集"""
        # 创建DataFrame
        rows = []
        for cat in self.split_info['test_data']:
            cat_id = cat['cat_id']
            images = cat['images']
            
            for img_path in images:
                relative_path = os.path.relpath(img_path, '../dataset/cat_individual_images')
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        df = pd.DataFrame(rows)
        root_path = '../dataset/cat_individual_images'
        
        # 测试时不使用数据增强
        transform = T.Compose([
            T.Resize([224, 224]),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        dataset = MemoryEfficientImageDataset(df, root_path, transform=transform, cache_size=200)
        
        logger.info(f"测试数据集创建完成: {len(dataset)} 张图片, {dataset.num_classes} 个类别")
        return dataset
    
    def _create_backbone(self):
        """创建骨干网络"""
        # 使用较小的MegaDescriptor模型
        backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',  # T版本比L版本小
            num_classes=0, 
            pretrained=True
        )
        
        backbone = backbone.to(self.device)
        
        # 获取特征维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
            features = backbone(dummy_input)
            self.feature_dim = features.shape[1]
        
        logger.info(f"骨干网络创建完成: 特征维度 {self.feature_dim}")
        
        # 清理临时变量
        del dummy_input, features
        if self.device == 'cuda':
            torch.cuda.empty_cache()
        
        return backbone
    
    def _create_objective(self):
        """创建损失函数"""
        # 使用ArcFace损失，参数优化
        objective = ArcFaceLoss(
            num_classes=self.train_dataset.num_classes,
            embedding_size=self.feature_dim,
            margin=0.5,
            scale=32  # 降低scale减少内存使用
        )
        
        objective = objective.to(self.device)
        logger.info(f"ArcFace损失函数创建完成")
        return objective
    
    def _create_optimizer(self):
        """创建优化器"""
        # 组合参数
        params = itertools.chain(self.backbone.parameters(), self.objective.parameters())
        
        # 使用AdamW，内存效率更高
        optimizer = optim.AdamW(
            params=params,
            lr=1e-4,  # 较小的学习率
            weight_decay=1e-4,
            betas=(0.9, 0.999)
        )
        
        logger.info(f"AdamW优化器创建完成")
        return optimizer
    
    def _create_scheduler(self):
        """创建学习率调度器"""
        scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=10,
            eta_min=1e-6
        )
        
        logger.info(f"余弦退火调度器创建完成")
        return scheduler
    
    def train(self, epochs: int = 10, save_path: str = 'wildlife_gpu_optimized.pth'):
        """GPU优化训练"""
        logger.info(f"开始GPU优化训练: {epochs} epochs")
        
        # 内存监控
        if self.device == 'cuda':
            initial_memory = torch.cuda.memory_allocated() / 1024**2
            logger.info(f"训练前GPU内存使用: {initial_memory:.1f}MB")
        
        # 设置随机种子
        set_seed(42)
        
        # 创建BasicTrainer，使用内存优化参数
        trainer = BasicTrainer(
            dataset=self.train_dataset,
            model=self.backbone,
            objective=self.objective,
            optimizer=self.optimizer,
            scheduler=self.scheduler,
            epochs=epochs,
            device=self.device,
            batch_size=6,      # 小批次
            num_workers=1,     # 减少worker
            accumulation_steps=6  # 梯度累积模拟大批次
        )
        
        # 内存监控回调
        def memory_callback():
            if self.device == 'cuda':
                current_memory = torch.cuda.memory_allocated() / 1024**2
                max_memory = torch.cuda.max_memory_allocated() / 1024**2
                logger.info(f"当前GPU内存: {current_memory:.1f}MB, 峰值: {max_memory:.1f}MB")
                
                # 如果内存使用过高，清理缓存
                if current_memory > 2000:  # 2GB阈值
                    torch.cuda.empty_cache()
                    gc.collect()
        
        # 开始训练
        start_time = time.time()
        
        try:
            trainer.train()
            training_time = time.time() - start_time
            logger.info(f"训练完成! 用时: {training_time:.1f}秒")
            
            # 保存模型
            self.save_model(save_path)
            
            return trainer
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                logger.error("GPU内存不足！尝试以下解决方案：")
                logger.error("1. 减少batch_size")
                logger.error("2. 减少accumulation_steps")
                logger.error("3. 使用更小的模型")
                raise
            else:
                raise
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'backbone_state_dict': self.backbone.state_dict(),
            'objective_state_dict': self.objective.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': self.feature_dim,
            'num_classes': self.train_dataset.num_classes,
            'split_file': 'dataset_split.json'
        }, save_path)
        
        logger.info(f"模型已保存: {save_path}")
    
    def evaluate(self, model_path: str = None):
        """评估模型"""
        if model_path:
            checkpoint = torch.load(model_path, map_location=self.device)
            self.backbone.load_state_dict(checkpoint['backbone_state_dict'])
        
        self.backbone.eval()
        
        logger.info("开始评估...")
        
        # 内存优化的特征提取
        extractor = DeepFeatures(self.backbone, batch_size=8)  # 小批次
        
        logger.info("提取训练集特征...")
        train_features = extractor(self.train_dataset)
        
        # 清理内存
        if self.device == 'cuda':
            torch.cuda.empty_cache()
        
        logger.info("提取测试集特征...")
        test_features = extractor(self.test_dataset)
        
        # 计算相似度
        logger.info("计算相似度...")
        similarity_function = CosineSimilarity()
        similarity = similarity_function(test_features, train_features)
        
        # 进行分类
        logger.info("进行分类...")
        classifier = KnnClassifier(k=1, database_labels=self.train_dataset.labels_string)
        predictions = classifier(similarity['cosine'])
        
        # 计算准确率
        accuracy = np.mean(self.test_dataset.labels_string == predictions)
        
        logger.info(f"评估完成!")
        logger.info(f"测试集准确率: {accuracy:.1%}")
        
        return {
            'accuracy': accuracy,
            'predictions': predictions,
            'true_labels': self.test_dataset.labels_string
        }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='GPU优化Wildlife-tools训练')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--epochs', type=int, default=8,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='wildlife_gpu_optimized.pth',
                       help='模型保存路径')
    parser.add_argument('--evaluate', action='store_true',
                       help='训练后进行评估')
    
    args = parser.parse_args()
    
    try:
        # 创建训练器
        trainer = GPUOptimizedTrainer(split_file=args.split_file)
        
        # 开始训练
        basic_trainer = trainer.train(epochs=args.epochs, save_path=args.output)
        
        # 评估
        if args.evaluate:
            results = trainer.evaluate(args.output)
            
            print(f"\n🎉 GPU优化Wildlife-tools训练完成!")
            print(f"📊 测试集准确率: {results['accuracy']:.1%}")
            
            # 保存评估结果
            eval_results = {
                'accuracy': float(results['accuracy']),
                'epochs': args.epochs,
                'device': trainer.device,
                'timestamp': time.time()
            }
            
            eval_path = args.output.replace('.pth', '_evaluation.json')
            with open(eval_path, 'w') as f:
                json.dump(eval_results, f, indent=2)
            
            logger.info(f"评估结果已保存: {eval_path}")
    
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise

if __name__ == "__main__":
    main()
