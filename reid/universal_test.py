#!/usr/bin/env python3
"""
通用测试脚本 - 验证任意规模下的识别性能
支持不同规模的猫咪数量测试
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from universal_cat_recognizer import create_universal_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_universal_test(dataset_path: str, cat_counts: List[int] = [3, 5, 10, 20], rounds: int = 3):
    """运行通用测试"""
    dataset_path = Path(dataset_path)

    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue

        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))

        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))

    available_cats.sort(key=lambda x: len(x[1]), reverse=True)

    print("🚀 通用猫咪识别系统测试")
    print("=" * 60)
    print(f"可用猫咪数量: {len(available_cats)}")

    results = {}

    for cat_count in cat_counts:
        if cat_count > len(available_cats):
            print(f"⚠️ 跳过 {cat_count} 只猫咪测试：可用数据不足")
            continue

        print(f"\n🎯 测试 {cat_count} 只猫咪")
        print("-" * 40)

        round_results = []

        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮测试...")

            # 创建识别器
            recognizer = create_universal_recognizer()

            # 随机选择猫咪
            selected_cats = random.sample(available_cats, cat_count)

            # 注册猫咪
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]

                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))

            # 识别测试
            correct = 0
            total = 0
            confidences = []
            response_times = []

            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)

                start_time = time.time()
                result = recognizer.recognize_cat(test_image)
                response_time = time.time() - start_time

                response_times.append(response_time)
                total += 1

                if result.get('success') and result.get('cat_id') == cat_id:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))

            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_response_time = np.mean(response_times) if response_times else 0.0

            round_results.append({
                'accuracy': accuracy,
                'confidence': avg_confidence,
                'response_time': avg_response_time,
                'correct': correct,
                'total': total
            })

            print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}")

        # 计算统计
        accuracies = [r['accuracy'] for r in round_results]
        avg_accuracy = np.mean(accuracies)
        accuracy_std = np.std(accuracies)

        results[cat_count] = {
            'avg_accuracy': avg_accuracy,
            'accuracy_std': accuracy_std,
            'rounds': round_results,
            'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
            'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95)
        }

        print(f"✅ 平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
        print(f"   高准确率轮次: {results[cat_count]['high_accuracy_rounds']}/{rounds}")

    return results

def display_summary(results: Dict):
    """显示测试总结"""
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)

    if not results:
        print("没有测试结果")
        return

    print(f"{'规模':<8} {'平均准确率':<12} {'稳定性':<10} {'高准确率轮次':<12}")
    print("-" * 50)

    for scale in sorted(results.keys()):
        result = results[scale]
        stability = 1.0 - result['accuracy_std']
        high_acc_ratio = result['high_accuracy_rounds'] / len(result['rounds'])

        print(f"{scale:<8} {result['avg_accuracy']:<12.1%} {stability:<10.1%} {high_acc_ratio:<12.1%}")

    # 系统评价
    min_accuracy = min(results[scale]['avg_accuracy'] for scale in results)
    max_scale = max(results.keys())

    print(f"\n🎯 系统评价:")
    print(f"   最大测试规模: {max_scale} 只猫咪")
    print(f"   最低准确率: {min_accuracy:.1%}")

    if min_accuracy >= 0.95:
        rating = "🌟 优秀 - 支持任意规模高精度识别"
    elif min_accuracy >= 0.9:
        rating = "✅ 良好 - 具备良好的规模扩展性"
    elif min_accuracy >= 0.8:
        rating = "⚠️ 一般 - 大规模场景需要优化"
    else:
        rating = "❌ 需要改进 - 规模扩展性不足"

    print(f"   总体评价: {rating}")

    # 问题诊断
    if min_accuracy == 0.0:
        print(f"\n⚠️ 问题诊断:")
        print(f"   所有测试准确率为0%，可能的原因:")
        print(f"   • 模型输入尺寸不匹配")
        print(f"   • 特征提取失败")
        print(f"   • 图片预处理问题")
        print(f"   建议检查模型配置和图片预处理流程")

    def _run_single_round(self, selected_cats: List[Tuple[str, List[str]]], round_num: int) -> Dict:
        """运行单轮测试"""
        # 创建新的识别器实例
        recognizer = create_universal_recognizer()
        
        # 注册猫咪
        registered_cats = []
        for cat_id, image_paths in selected_cats:
            cat_name = f"TestCat_{cat_id}"
            
            # 使用前70%的图片进行注册
            train_count = max(3, int(len(image_paths) * 0.7))
            train_images = image_paths[:train_count]
            
            result = recognizer.register_cat(cat_id, cat_name, train_images)
            if result['success']:
                registered_cats.append((cat_id, image_paths[train_count:]))  # 剩余图片用于测试
        
        if len(registered_cats) < 2:
            return {
                'accuracy': 0.0,
                'avg_confidence': 0.0,
                'avg_response_time': 0.0,
                'total_tests': 0,
                'correct_tests': 0
            }
        
        # 识别测试
        test_results = []
        response_times = []
        confidences = []
        
        for cat_id, test_images in registered_cats:
            if not test_images:
                continue
            
            # 随机选择一张测试图片
            test_image = random.choice(test_images)
            
            # 进行识别
            start_time = time.time()
            result = recognizer.recognize_cat(test_image)
            response_time = time.time() - start_time
            
            response_times.append(response_time)
            
            # 判断结果
            is_correct = (result.get('success', False) and 
                         result.get('cat_id') == cat_id)
            
            test_results.append(is_correct)
            
            if result.get('success', False):
                confidences.append(result.get('confidence', 0.0))
            
            # 更新性能跟踪
            recognizer.similarity_matcher.update_performance(cat_id, is_correct)
        
        # 计算统计结果
        accuracy = np.mean(test_results) if test_results else 0.0
        avg_confidence = np.mean(confidences) if confidences else 0.0
        avg_response_time = np.mean(response_times) if response_times else 0.0
        
        return {
            'accuracy': accuracy,
            'avg_confidence': avg_confidence,
            'avg_response_time': avg_response_time,
            'total_tests': len(test_results),
            'correct_tests': sum(test_results)
        }
    
    def run_comprehensive_test(self) -> Dict:
        """运行综合测试"""
        logger.info("🚀 开始通用识别系统综合测试")
        
        # 测试不同规模
        cat_counts = [3, 5, 10, 20, 50, 100]
        
        # 运行规模测试
        scale_results = self.run_scale_test(cat_counts, rounds_per_scale=3)
        
        # 分析结果
        analysis = self._analyze_results(scale_results)
        
        return {
            'scale_results': scale_results,
            'analysis': analysis,
            'timestamp': time.time()
        }
    
    def _analyze_results(self, scale_results: Dict) -> Dict:
        """分析测试结果"""
        analysis = {
            'scalability': {},
            'performance_trends': {},
            'recommendations': []
        }
        
        # 可扩展性分析
        scales = sorted(scale_results.keys())
        accuracies = [scale_results[scale]['avg_accuracy'] for scale in scales]
        
        # 计算性能下降趋势
        if len(accuracies) > 1:
            performance_drop = accuracies[0] - accuracies[-1]
            analysis['scalability']['performance_drop'] = performance_drop
            analysis['scalability']['maintains_90_percent'] = min(accuracies) >= 0.9
            analysis['scalability']['maintains_95_percent'] = min(accuracies) >= 0.95
        
        # 性能趋势
        for i, scale in enumerate(scales):
            result = scale_results[scale]
            analysis['performance_trends'][scale] = {
                'accuracy': result['avg_accuracy'],
                'stability': 1.0 - result['accuracy_std'],  # 稳定性指标
                'reliability': result['high_accuracy_rounds'] / len(result['rounds'])
            }
        
        # 生成建议
        best_scale = max(scales, key=lambda s: scale_results[s]['avg_accuracy'])
        worst_scale = min(scales, key=lambda s: scale_results[s]['avg_accuracy'])
        
        analysis['recommendations'].append(f"最佳性能规模: {best_scale} 只猫咪")
        analysis['recommendations'].append(f"最具挑战性规模: {worst_scale} 只猫咪")
        
        if min(accuracies) >= 0.95:
            analysis['recommendations'].append("✅ 系统在所有测试规模下都达到了95%以上准确率")
        elif min(accuracies) >= 0.9:
            analysis['recommendations'].append("✅ 系统在所有测试规模下都达到了90%以上准确率")
        else:
            analysis['recommendations'].append("⚠️ 系统在大规模场景下性能有所下降，建议进一步优化")
        
        return analysis

def display_results(results: Dict):
    """显示测试结果"""
    print("\n" + "=" * 80)
    print("📊 通用识别系统测试结果")
    print("=" * 80)
    
    scale_results = results['scale_results']
    analysis = results['analysis']
    
    # 显示各规模结果
    print("\n📈 不同规模性能表现:")
    print("-" * 60)
    print(f"{'规模':<8} {'平均准确率':<12} {'稳定性':<10} {'高准确率轮次':<12} {'响应时间':<10}")
    print("-" * 60)
    
    for scale in sorted(scale_results.keys()):
        result = scale_results[scale]
        stability = 1.0 - result['accuracy_std']
        high_acc_ratio = result['high_accuracy_rounds'] / len(result['rounds'])
        
        print(f"{scale:<8} {result['avg_accuracy']:<12.1%} {stability:<10.1%} "
              f"{high_acc_ratio:<12.1%} {result['avg_response_time']:<10.3f}s")
    
    # 显示分析结果
    print(f"\n🔍 系统分析:")
    scalability = analysis['scalability']
    if 'performance_drop' in scalability:
        print(f"   性能下降幅度: {scalability['performance_drop']:.1%}")
        print(f"   保持90%准确率: {'✅' if scalability['maintains_90_percent'] else '❌'}")
        print(f"   保持95%准确率: {'✅' if scalability['maintains_95_percent'] else '❌'}")
    
    print(f"\n💡 系统建议:")
    for recommendation in analysis['recommendations']:
        print(f"   • {recommendation}")
    
    # 总体评价
    min_accuracy = min(scale_results[scale]['avg_accuracy'] for scale in scale_results)
    if min_accuracy >= 0.95:
        overall_rating = "🌟 优秀 - 支持任意规模高精度识别"
    elif min_accuracy >= 0.9:
        overall_rating = "✅ 良好 - 具备良好的规模扩展性"
    elif min_accuracy >= 0.8:
        overall_rating = "⚠️ 一般 - 大规模场景需要优化"
    else:
        overall_rating = "❌ 需要改进 - 规模扩展性不足"
    
    print(f"\n🎯 系统总体评价: {overall_rating}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='通用识别系统测试')
    parser.add_argument('--dataset', type=str,
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='universal_test_results.json',
                       help='结果保存路径')

    args = parser.parse_args()

    # 运行测试
    results = run_universal_test(args.dataset, args.scales, args.rounds)

    # 显示结果
    display_summary(results)

    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n💾 测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
