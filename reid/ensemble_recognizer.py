#!/usr/bin/env python3
"""
集成识别器 - 结合多个模型和策略提升性能
"""

import os
import sys
import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
from collections import defaultdict
from PIL import Image
import torchvision.transforms as transforms

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_recognizer import create_enhanced_recognizer
from production_recognizer import create_production_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleRecognizer:
    """集成识别器 - 结合多个模型和策略"""
    
    def __init__(self, model_paths: List[str], weights: List[float] = None, device='cpu'):
        self.device = device
        self.model_paths = model_paths
        
        # 模型权重
        if weights is None:
            self.weights = [1.0 / len(model_paths)] * len(model_paths)
        else:
            self.weights = weights
        
        # 创建多个识别器
        self.recognizers = []
        for i, model_path in enumerate(model_paths):
            try:
                if model_path:
                    recognizer = create_enhanced_recognizer(model_path, device)
                else:
                    recognizer = create_enhanced_recognizer(None, device)  # 基础模型
                self.recognizers.append(recognizer)
                logger.info(f"成功加载模型 {i+1}: {model_path if model_path else '基础模型'}")
            except Exception as e:
                logger.error(f"加载模型失败 {model_path}: {e}")
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 优化后的阈值函数
        self.optimized_thresholds = self._load_optimized_thresholds()
        
        logger.info(f"集成识别器初始化完成: {len(self.recognizers)} 个模型")
    
    def _load_optimized_thresholds(self) -> Dict:
        """加载优化后的阈值"""
        # 默认阈值，实际应该从阈值优化结果中加载
        return {
            3: 0.70,
            5: 0.65,
            10: 0.60,
            20: 0.55,
            30: 0.50,
            50: 0.45
        }
    
    def get_optimized_threshold(self, num_cats: int) -> float:
        """获取优化后的阈值"""
        if num_cats in self.optimized_thresholds:
            return self.optimized_thresholds[num_cats]
        
        # 线性插值
        sorted_scales = sorted(self.optimized_thresholds.keys())
        
        if num_cats <= sorted_scales[0]:
            return self.optimized_thresholds[sorted_scales[0]]
        elif num_cats >= sorted_scales[-1]:
            return self.optimized_thresholds[sorted_scales[-1]]
        else:
            for i in range(len(sorted_scales) - 1):
                if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                    x1, y1 = sorted_scales[i], self.optimized_thresholds[sorted_scales[i]]
                    x2, y2 = sorted_scales[i + 1], self.optimized_thresholds[sorted_scales[i + 1]]
                    return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
        
        return 0.60  # 默认值
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """在所有模型中注册猫咪"""
        results = []
        
        for i, recognizer in enumerate(self.recognizers):
            try:
                result = recognizer.register_cat(cat_id, cat_name, image_paths)
                results.append(result)
            except Exception as e:
                logger.error(f"模型 {i+1} 注册失败: {e}")
                results.append({'success': False, 'error': str(e)})
        
        # 统计成功率
        successful = sum(1 for r in results if r.get('success', False))
        
        if successful > 0:
            # 保存到数据库
            self.cat_database[cat_id] = {
                'name': cat_name,
                'registration_time': time.time(),
                'successful_models': successful
            }
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'successful_models': successful,
                'total_models': len(self.recognizers)
            }
        else:
            return {
                'success': False,
                'error': '所有模型注册失败',
                'results': results
            }
    
    def recognize_cat(self, image_path: str) -> Dict:
        """集成识别"""
        start_time = time.time()
        
        # 收集所有模型的结果
        model_results = []
        
        for i, recognizer in enumerate(self.recognizers):
            try:
                result = recognizer.recognize_cat(image_path)
                model_results.append({
                    'model_id': i,
                    'weight': self.weights[i],
                    'result': result
                })
            except Exception as e:
                logger.error(f"模型 {i+1} 识别失败: {e}")
                model_results.append({
                    'model_id': i,
                    'weight': self.weights[i],
                    'result': {'success': False, 'error': str(e)}
                })
        
        # 集成策略
        ensemble_result = self._ensemble_predictions(model_results)
        ensemble_result['response_time'] = time.time() - start_time
        
        return ensemble_result
    
    def _ensemble_predictions(self, model_results: List[Dict]) -> Dict:
        """集成预测结果"""
        # 收集所有成功的预测
        successful_results = [mr for mr in model_results 
                            if mr['result'].get('success', False)]
        
        if not successful_results:
            return {
                'success': False,
                'status': 'all_models_failed',
                'error': '所有模型识别失败'
            }
        
        # 策略1: 加权投票
        cat_votes = defaultdict(float)
        cat_confidences = defaultdict(list)
        cat_similarities = defaultdict(list)
        
        for mr in successful_results:
            result = mr['result']
            weight = mr['weight']
            cat_id = result.get('cat_id')
            confidence = result.get('confidence', 0.0)
            similarity = result.get('similarity', 0.0)
            
            if cat_id:
                cat_votes[cat_id] += weight
                cat_confidences[cat_id].append(confidence)
                cat_similarities[cat_id].append(similarity)
        
        if not cat_votes:
            return {
                'success': False,
                'status': 'no_valid_predictions',
                'error': '没有有效的预测结果'
            }
        
        # 找到得票最高的猫咪
        best_cat_id = max(cat_votes.keys(), key=lambda x: cat_votes[x])
        best_vote_score = cat_votes[best_cat_id]
        
        # 计算集成置信度和相似度
        ensemble_confidence = np.mean(cat_confidences[best_cat_id])
        ensemble_similarity = np.mean(cat_similarities[best_cat_id])
        
        # 获取优化后的阈值
        num_cats = len(self.cat_database)
        optimized_threshold = self.get_optimized_threshold(num_cats)
        
        # 集成决策
        vote_threshold = 0.5  # 投票阈值
        similarity_threshold = optimized_threshold
        
        if (best_vote_score >= vote_threshold and 
            ensemble_similarity >= similarity_threshold):
            
            cat_info = self.cat_database.get(best_cat_id, {'name': f'Cat_{best_cat_id}'})
            
            return {
                'success': True,
                'cat_id': best_cat_id,
                'cat_name': cat_info['name'],
                'confidence': ensemble_confidence,
                'similarity': ensemble_similarity,
                'vote_score': best_vote_score,
                'threshold_used': similarity_threshold,
                'status': 'recognized',
                'ensemble_info': {
                    'successful_models': len(successful_results),
                    'total_models': len(model_results),
                    'voting_results': dict(cat_votes)
                }
            }
        else:
            return {
                'success': False,
                'status': 'low_confidence',
                'best_candidate': {
                    'cat_id': best_cat_id,
                    'cat_name': self.cat_database.get(best_cat_id, {'name': f'Cat_{best_cat_id}'})['name'],
                    'vote_score': best_vote_score,
                    'similarity': ensemble_similarity
                },
                'threshold_used': similarity_threshold,
                'error': f'投票分数或相似度过低 (投票:{best_vote_score:.3f}, 相似度:{ensemble_similarity:.3f})'
            }
    
    def get_system_stats(self) -> Dict:
        """获取系统统计"""
        # 收集所有模型的统计
        model_stats = []
        for i, recognizer in enumerate(self.recognizers):
            try:
                stats = recognizer.get_system_stats()
                model_stats.append({
                    'model_id': i,
                    'stats': stats
                })
            except Exception as e:
                logger.error(f"获取模型 {i+1} 统计失败: {e}")
        
        return {
            'registered_cats': len(self.cat_database),
            'active_models': len(self.recognizers),
            'model_stats': model_stats,
            'optimized_threshold': self.get_optimized_threshold(len(self.cat_database))
        }

class EnsembleTester:
    """集成测试器"""
    
    def __init__(self, dataset_path: str, model_paths: List[str], weights: List[float] = None):
        self.dataset_path = Path(dataset_path)
        self.model_paths = model_paths
        self.weights = weights
        
        # 加载数据
        self.available_cats = self._load_cat_data()
        
        logger.info(f"集成测试器初始化完成: {len(self.available_cats)} 只可用猫咪")
    
    def _load_cat_data(self) -> List[Tuple[str, List[str]]]:
        """加载猫咪数据"""
        cats = []
        for cat_folder in self.dataset_path.iterdir():
            if not cat_folder.is_dir() or not cat_folder.name.isdigit():
                continue
            
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                cats.append((cat_folder.name, [str(img) for img in images]))
        
        cats.sort(key=lambda x: len(x[1]), reverse=True)
        return cats
    
    def test_ensemble(self, scales: List[int], rounds: int = 3) -> Dict:
        """测试集成性能"""
        print("🚀 集成识别器测试")
        print("=" * 60)
        print(f"模型数量: {len(self.model_paths)}")
        print(f"测试规模: {scales}")
        
        results = {}
        
        for scale in scales:
            if scale > len(self.available_cats):
                print(f"⚠️ 跳过规模 {scale}: 数据不足")
                continue
            
            print(f"\n🎯 测试规模: {scale} 只猫咪")
            print("-" * 40)
            
            scale_results = []
            
            for round_num in range(rounds):
                print(f"第 {round_num + 1} 轮...")
                
                # 创建集成识别器
                ensemble = EnsembleRecognizer(self.model_paths, self.weights, device='cpu')
                
                # 随机选择猫咪
                selected_cats = random.sample(self.available_cats, scale)
                
                # 注册猫咪
                registered_cats = []
                for cat_id, image_paths in selected_cats:
                    train_count = max(3, int(len(image_paths) * 0.7))
                    train_images = image_paths[:train_count]
                    test_images = image_paths[train_count:]
                    
                    result = ensemble.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                    if result['success'] and test_images:
                        registered_cats.append((cat_id, test_images))
                
                # 识别测试
                correct = 0
                total = 0
                confidences = []
                similarities = []
                vote_scores = []
                
                for cat_id, test_images in registered_cats:
                    test_image = random.choice(test_images)
                    result = ensemble.recognize_cat(test_image)
                    
                    total += 1
                    is_correct = result.get('success') and result.get('cat_id') == cat_id
                    
                    if is_correct:
                        correct += 1
                        confidences.append(result.get('confidence', 0.0))
                    
                    similarities.append(result.get('similarity', 0.0))
                    vote_scores.append(result.get('vote_score', 0.0))
                
                accuracy = correct / total if total > 0 else 0.0
                avg_confidence = np.mean(confidences) if confidences else 0.0
                avg_similarity = np.mean(similarities) if similarities else 0.0
                avg_vote_score = np.mean(vote_scores) if vote_scores else 0.0
                
                scale_results.append({
                    'accuracy': accuracy,
                    'avg_confidence': avg_confidence,
                    'avg_similarity': avg_similarity,
                    'avg_vote_score': avg_vote_score,
                    'correct': correct,
                    'total': total
                })
                
                print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, "
                      f"相似度: {avg_similarity:.3f}, 投票分数: {avg_vote_score:.3f}")
            
            # 计算统计
            accuracies = [r['accuracy'] for r in scale_results]
            avg_accuracy = np.mean(accuracies)
            accuracy_std = np.std(accuracies)
            
            results[scale] = {
                'avg_accuracy': avg_accuracy,
                'accuracy_std': accuracy_std,
                'rounds': scale_results,
                'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95)
            }
            
            print(f"✅ 平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
        
        return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='集成识别器测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--models', type=str, nargs='+', 
                       default=[None, 'training/enhanced_megadescriptor_50cats.pth'],
                       help='模型路径列表')
    parser.add_argument('--weights', type=float, nargs='+', default=None,
                       help='模型权重列表')
    parser.add_argument('--scales', type=int, nargs='+', default=[5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = EnsembleTester(args.dataset, args.models, args.weights)
    
    # 运行测试
    results = tester.test_ensemble(args.scales, args.rounds)
    
    # 保存结果
    import json
    with open('ensemble_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 集成测试结果已保存: ensemble_test_results.json")

if __name__ == "__main__":
    main()
