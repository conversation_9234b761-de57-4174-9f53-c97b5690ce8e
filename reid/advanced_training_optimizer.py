#!/usr/bin/env python3
"""
高级训练优化器 - 针对大规模识别场景的深度优化
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HardTripletDataset(Dataset):
    """困难三元组数据集 - 专门针对大规模场景优化"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100, hard_negative_ratio: float = 0.7):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        self.hard_negative_ratio = hard_negative_ratio
        
        # 更强的数据增强
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=25),
            transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2),
            transforms.RandomAffine(degrees=0, translate=(0.2, 0.2), scale=(0.8, 1.2)),
            transforms.RandomPerspective(distortion_scale=0.15, p=0.4),
            transforms.RandomGrayscale(p=0.1),
            transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.cat_images = defaultdict(list)
        self.cat_ids = []
        self._build_dataset()
        
        # 预计算困难负样本
        self.hard_negatives = self._compute_hard_negatives()
        
        logger.info(f"困难三元组数据集构建完成: {len(self.cat_ids)} 只猫咪")
    
    def _build_dataset(self):
        """构建数据集"""
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                valid_cats.append((cat_folder.name, images))
        
        # 选择数据最丰富的猫咪
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        selected_cats = valid_cats[:self.max_cats]
        
        for cat_id, images in selected_cats:
            self.cat_images[cat_id] = [str(img) for img in images]
            self.cat_ids.append(cat_id)
    
    def _compute_hard_negatives(self):
        """预计算困难负样本对"""
        # 这里可以基于预训练模型计算相似度，找到困难负样本
        # 简化版本：随机选择，实际应用中可以用模型预计算
        hard_negatives = defaultdict(list)
        
        for cat_id in self.cat_ids:
            # 为每只猫咪找到最相似的其他猫咪作为困难负样本
            other_cats = [c for c in self.cat_ids if c != cat_id]
            hard_negatives[cat_id] = random.sample(other_cats, min(10, len(other_cats)))
        
        return hard_negatives
    
    def __len__(self):
        return len(self.cat_ids) * 100  # 增加数据量
    
    def __getitem__(self, idx):
        """生成困难三元组"""
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择anchor和positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择困难负样本
        if random.random() < self.hard_negative_ratio and anchor_cat in self.hard_negatives:
            negative_cat = random.choice(self.hard_negatives[anchor_cat])
        else:
            negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative, anchor_cat, negative_cat
        except Exception as e:
            logger.warning(f"加载图片失败: {e}")
            return self.__getitem__(random.randint(0, len(self) - 1))

class AdvancedMegaDescriptor(nn.Module):
    """高级MegaDescriptor - 针对大规模场景优化"""
    
    def __init__(self, feature_dim=768):  # 增加特征维度
        super().__init__()
        
        # 使用更大的骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-L-384',  # 使用Large版本
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 384, 384)  # L版本使用384x384
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 多层特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.2),
            
            # 第二层：压缩
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            
            # 第三层：精炼
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.05),
            
            # 输出层
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(feature_dim, num_heads=8, batch_first=True)
        
        logger.info(f"高级MegaDescriptor初始化完成: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        # 调整输入尺寸为384x384
        if x.shape[-1] != 384:
            x = torch.nn.functional.interpolate(x, size=(384, 384), mode='bilinear', align_corners=False)
        
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # 自注意力机制
        enhanced_features_unsqueezed = enhanced_features.unsqueeze(1)
        attended_features, _ = self.attention(
            enhanced_features_unsqueezed, 
            enhanced_features_unsqueezed, 
            enhanced_features_unsqueezed
        )
        attended_features = attended_features.squeeze(1)
        
        # 残差连接
        final_features = enhanced_features + attended_features
        
        # L2归一化
        return torch.nn.functional.normalize(final_features, p=2, dim=1)

class FocalTripletLoss(nn.Module):
    """焦点三元组损失 - 专注于困难样本"""
    
    def __init__(self, margin=0.5, alpha=2.0):
        super().__init__()
        self.margin = margin
        self.alpha = alpha
    
    def forward(self, anchor, positive, negative):
        # 计算距离
        pos_dist = torch.norm(anchor - positive, p=2, dim=1)
        neg_dist = torch.norm(anchor - negative, p=2, dim=1)
        
        # 基础三元组损失
        basic_loss = torch.clamp(pos_dist - neg_dist + self.margin, min=0.0)
        
        # 焦点权重：困难样本获得更高权重
        focal_weight = torch.pow(basic_loss / self.margin, self.alpha)
        
        # 焦点损失
        focal_loss = focal_weight * basic_loss
        
        return focal_loss.mean()

class AdvancedTrainingOptimizer:
    """高级训练优化器"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100, device='cuda'):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 创建数据集
        self.dataset = HardTripletDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=8,  # 较小batch size适应更大模型
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        
        # 创建高级模型
        self.model = AdvancedMegaDescriptor(feature_dim=768).to(self.device)
        
        # 焦点三元组损失
        self.triplet_loss = FocalTripletLoss(margin=0.5, alpha=2.0)
        
        # 高级优化器配置
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 5e-6},  # 骨干网络极小学习率
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-3},  # 增强网络正常学习率
            {'params': self.model.attention.parameters(), 'lr': 5e-4}  # 注意力机制中等学习率
        ], weight_decay=1e-4)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.OneCycleLR(
            self.optimizer, 
            max_lr=[5e-6, 1e-3, 5e-4],
            epochs=15,
            steps_per_epoch=len(self.dataloader),
            pct_start=0.1
        )
        
        logger.info(f"高级训练优化器初始化完成")
    
    def train_epoch(self, epoch: int) -> Dict:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative, anchor_cats, negative_cats) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_features = self.model(anchor)
            positive_features = self.model(positive)
            negative_features = self.model(negative)
            
            # 计算焦点三元组损失
            loss = self.triplet_loss(anchor_features, positive_features, negative_features)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            self.scheduler.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 打印进度
            if batch_idx % 50 == 0:
                current_lr = self.scheduler.get_last_lr()
                logger.info(f"Epoch {epoch}, Batch {batch_idx}/{len(self.dataloader)}: "
                          f"Loss={loss.item():.4f}, LR={current_lr[1]:.2e}")
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        return {
            'loss': avg_loss,
            'learning_rates': self.scheduler.get_last_lr()
        }
    
    def evaluate_model(self) -> Dict:
        """评估模型"""
        self.model.eval()
        
        eval_cats = random.sample(self.dataset.cat_ids, min(20, len(self.dataset.cat_ids)))
        
        intra_distances = []
        inter_distances = []
        
        with torch.no_grad():
            for cat_id in eval_cats:
                cat_images = self.dataset.cat_images[cat_id]
                
                if len(cat_images) >= 2:
                    img1_path, img2_path = random.sample(cat_images, 2)
                    
                    img1 = self.dataset.transform(Image.open(img1_path).convert('RGB')).unsqueeze(0).to(self.device)
                    img2 = self.dataset.transform(Image.open(img2_path).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1 = self.model(img1)
                    feat2 = self.model(img2)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_distances.append(intra_dist)
                
                # 类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img_path = random.choice(self.dataset.cat_images[other_cat])
                
                img1 = self.dataset.transform(Image.open(random.choice(cat_images)).convert('RGB')).unsqueeze(0).to(self.device)
                img2 = self.dataset.transform(Image.open(other_img_path).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1 = self.model(img1)
                feat2 = self.model(img2)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_distances.append(inter_dist)
        
        avg_intra_dist = np.mean(intra_distances) if intra_distances else 0.0
        avg_inter_dist = np.mean(inter_distances) if inter_distances else 0.0
        separability = avg_inter_dist / (avg_intra_dist + 1e-8)
        
        return {
            'avg_intra_distance': avg_intra_dist,
            'avg_inter_distance': avg_inter_dist,
            'separability': separability
        }
    
    def train(self, epochs: int = 15, save_path: str = 'advanced_megadescriptor.pth') -> Dict:
        """完整训练流程"""
        logger.info(f"开始高级训练优化: {epochs} epochs")
        
        training_history = {
            'epochs': [],
            'losses': [],
            'separabilities': []
        }
        
        best_separability = 0.0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            train_stats = self.train_epoch(epoch + 1)
            
            # 评估
            eval_stats = self.evaluate_model()
            
            # 记录历史
            training_history['epochs'].append(epoch + 1)
            training_history['losses'].append(train_stats['loss'])
            training_history['separabilities'].append(eval_stats['separability'])
            
            epoch_time = time.time() - start_time
            
            logger.info(f"Epoch {epoch + 1}/{epochs} 完成:")
            logger.info(f"  损失: {train_stats['loss']:.4f}")
            logger.info(f"  分离度: {eval_stats['separability']:.4f}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if eval_stats['separability'] > best_separability:
                best_separability = eval_stats['separability']
                self.save_model(save_path)
                logger.info(f"保存最佳模型: 分离度 {best_separability:.4f}")
        
        logger.info(f"高级训练完成! 最佳分离度: {best_separability:.4f}")
        
        return training_history
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'cat_ids': self.dataset.cat_ids,
            'num_cats': len(self.dataset.cat_ids)
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='高级训练优化')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=100,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=15,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='advanced_megadescriptor.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = AdvancedTrainingOptimizer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    history = trainer.train(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"高级训练优化完成，模型已保存: {args.output}")

if __name__ == "__main__":
    main()
