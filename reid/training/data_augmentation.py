#!/usr/bin/env python3
"""
高级数据增强策略 - 提升模型鲁棒性
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import random
import cv2
from typing import Tuple, List, Optional
import torchvision.transforms as transforms
import logging

logger = logging.getLogger(__name__)

class AdvancedDataAugmentation:
    """高级数据增强器"""
    
    def __init__(self, config: dict):
        """
        初始化数据增强器
        
        Args:
            config: 增强配置
        """
        self.config = config
        self.mixup_alpha = config.get('mixup_alpha', 0.2)
        self.cutmix_alpha = config.get('cutmix_alpha', 1.0)
        self.cutmix_prob = config.get('cutmix_prob', 0.5)
        self.augment_prob = config.get('augment_prob', 0.8)
        
        # 基础增强变换
        self.base_transforms = transforms.Compose([
            transforms.RandomRotation(15),
            transforms.RandomHorizontalFlip(0.5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
        ])
        
        # 高级增强变换
        self.advanced_transforms = [
            self._gaussian_blur,
            self._add_noise,
            self._adjust_lighting,
            self._elastic_transform,
            self._random_erasing
        ]
        
        logger.info("高级数据增强器初始化完成")
    
    def augment_batch(self, images: torch.Tensor, labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        对批次数据进行增强
        
        Args:
            images: 图像张量 [batch_size, 3, H, W]
            labels: 标签张量 [batch_size]
            
        Returns:
            增强后的图像和标签
        """
        batch_size = images.size(0)
        
        # 随机选择增强策略
        if random.random() < self.cutmix_prob:
            # CutMix增强
            images, labels = self._cutmix(images, labels)
        elif random.random() < 0.5:
            # MixUp增强
            images, labels = self._mixup(images, labels)
        
        # 应用其他增强
        if random.random() < self.augment_prob:
            images = self._apply_advanced_augmentations(images)
        
        return images, labels
    
    def _mixup(self, images: torch.Tensor, labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        MixUp数据增强
        
        Args:
            images: 图像张量
            labels: 标签张量
            
        Returns:
            混合后的图像和标签
        """
        batch_size = images.size(0)
        
        # 生成混合权重
        lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
        
        # 随机排列索引
        index = torch.randperm(batch_size)
        
        # 混合图像
        mixed_images = lam * images + (1 - lam) * images[index]
        
        # 创建软标签
        labels_a, labels_b = labels, labels[index]
        mixed_labels = lam * F.one_hot(labels_a, num_classes=self.config.get('num_classes', 100)).float() + \
                      (1 - lam) * F.one_hot(labels_b, num_classes=self.config.get('num_classes', 100)).float()
        
        return mixed_images, mixed_labels
    
    def _cutmix(self, images: torch.Tensor, labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        CutMix数据增强
        
        Args:
            images: 图像张量
            labels: 标签张量
            
        Returns:
            剪切混合后的图像和标签
        """
        batch_size = images.size(0)
        _, _, H, W = images.shape
        
        # 生成混合权重
        lam = np.random.beta(self.cutmix_alpha, self.cutmix_alpha)
        
        # 随机排列索引
        index = torch.randperm(batch_size)
        
        # 计算剪切区域
        cut_rat = np.sqrt(1. - lam)
        cut_w = int(W * cut_rat)
        cut_h = int(H * cut_rat)
        
        # 随机选择剪切位置
        cx = np.random.randint(W)
        cy = np.random.randint(H)
        
        bbx1 = np.clip(cx - cut_w // 2, 0, W)
        bby1 = np.clip(cy - cut_h // 2, 0, H)
        bbx2 = np.clip(cx + cut_w // 2, 0, W)
        bby2 = np.clip(cy + cut_h // 2, 0, H)
        
        # 执行剪切混合
        mixed_images = images.clone()
        mixed_images[:, :, bby1:bby2, bbx1:bbx2] = images[index, :, bby1:bby2, bbx1:bbx2]
        
        # 调整混合权重
        lam = 1 - ((bbx2 - bbx1) * (bby2 - bby1) / (W * H))
        
        # 创建软标签
        labels_a, labels_b = labels, labels[index]
        mixed_labels = lam * F.one_hot(labels_a, num_classes=self.config.get('num_classes', 100)).float() + \
                      (1 - lam) * F.one_hot(labels_b, num_classes=self.config.get('num_classes', 100)).float()
        
        return mixed_images, mixed_labels
    
    def _apply_advanced_augmentations(self, images: torch.Tensor) -> torch.Tensor:
        """应用高级增强技术"""
        augmented_images = []
        
        for img in images:
            # 转换为PIL图像
            img_pil = transforms.ToPILImage()(img)
            
            # 随机应用高级增强
            if random.random() < 0.3:
                transform_func = random.choice(self.advanced_transforms)
                img_pil = transform_func(img_pil)
            
            # 转换回张量
            img_tensor = transforms.ToTensor()(img_pil)
            augmented_images.append(img_tensor)
        
        return torch.stack(augmented_images)
    
    def _gaussian_blur(self, image: Image.Image) -> Image.Image:
        """高斯模糊"""
        radius = random.uniform(0.5, 2.0)
        return image.filter(ImageFilter.GaussianBlur(radius=radius))
    
    def _add_noise(self, image: Image.Image) -> Image.Image:
        """添加噪声"""
        img_array = np.array(image)
        noise = np.random.normal(0, 10, img_array.shape).astype(np.uint8)
        noisy_img = np.clip(img_array + noise, 0, 255)
        return Image.fromarray(noisy_img)
    
    def _adjust_lighting(self, image: Image.Image) -> Image.Image:
        """调整光照"""
        # 随机调整亮度和对比度
        brightness_factor = random.uniform(0.8, 1.2)
        contrast_factor = random.uniform(0.8, 1.2)
        
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(brightness_factor)
        
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(contrast_factor)
        
        return image
    
    def _elastic_transform(self, image: Image.Image) -> Image.Image:
        """弹性变换"""
        img_array = np.array(image)
        h, w = img_array.shape[:2]
        
        # 生成随机位移场
        dx = np.random.uniform(-1, 1, (h, w)) * 5
        dy = np.random.uniform(-1, 1, (h, w)) * 5
        
        # 创建网格
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        x_new = np.clip(x + dx, 0, w-1).astype(np.float32)
        y_new = np.clip(y + dy, 0, h-1).astype(np.float32)
        
        # 应用变换
        transformed = cv2.remap(img_array, x_new, y_new, cv2.INTER_LINEAR)
        return Image.fromarray(transformed)
    
    def _random_erasing(self, image: Image.Image) -> Image.Image:
        """随机擦除"""
        img_array = np.array(image)
        h, w = img_array.shape[:2]
        
        # 随机选择擦除区域
        area = h * w
        target_area = random.uniform(0.02, 0.4) * area
        aspect_ratio = random.uniform(0.3, 3.3)
        
        h_erase = int(round(np.sqrt(target_area * aspect_ratio)))
        w_erase = int(round(np.sqrt(target_area / aspect_ratio)))
        
        if h_erase < h and w_erase < w:
            x1 = random.randint(0, w - w_erase)
            y1 = random.randint(0, h - h_erase)
            
            # 用随机值填充
            img_array[y1:y1+h_erase, x1:x1+w_erase] = np.random.randint(0, 256, 
                                                                        (h_erase, w_erase, img_array.shape[2]))
        
        return Image.fromarray(img_array)

class HardSampleMining:
    """困难样本挖掘"""
    
    def __init__(self, config: dict):
        """
        初始化困难样本挖掘器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.loss_history = []
        self.sample_difficulties = {}
        
    def update_sample_difficulty(self, sample_ids: List[str], losses: List[float]):
        """更新样本难度"""
        for sample_id, loss in zip(sample_ids, losses):
            if sample_id not in self.sample_difficulties:
                self.sample_difficulties[sample_id] = []
            
            self.sample_difficulties[sample_id].append(loss)
            
            # 只保留最近的10个损失值
            if len(self.sample_difficulties[sample_id]) > 10:
                self.sample_difficulties[sample_id] = self.sample_difficulties[sample_id][-10:]
    
    def get_hard_samples(self, top_k: int = 100) -> List[str]:
        """获取最困难的样本"""
        # 计算每个样本的平均损失
        avg_difficulties = {}
        for sample_id, losses in self.sample_difficulties.items():
            avg_difficulties[sample_id] = np.mean(losses)
        
        # 按难度排序
        sorted_samples = sorted(avg_difficulties.items(), key=lambda x: x[1], reverse=True)
        
        # 返回最困难的top_k个样本
        return [sample_id for sample_id, _ in sorted_samples[:top_k]]
    
    def should_focus_on_hard_samples(self, epoch: int) -> bool:
        """判断是否应该专注于困难样本"""
        # 在训练后期更多关注困难样本
        return epoch > self.config.get('hard_sample_start_epoch', 10)

def create_augmentation_pipeline(config: dict) -> AdvancedDataAugmentation:
    """创建数据增强管道"""
    return AdvancedDataAugmentation(config)

if __name__ == "__main__":
    # 测试数据增强
    config = {
        'mixup_alpha': 0.2,
        'cutmix_alpha': 1.0,
        'cutmix_prob': 0.5,
        'augment_prob': 0.8,
        'num_classes': 10
    }
    
    augmenter = create_augmentation_pipeline(config)
    
    # 测试输入
    batch_size = 4
    images = torch.randn(batch_size, 3, 384, 384)
    labels = torch.randint(0, 10, (batch_size,))
    
    # 应用增强
    aug_images, aug_labels = augmenter.augment_batch(images, labels)
    
    print(f"原始图像形状: {images.shape}")
    print(f"增强图像形状: {aug_images.shape}")
    print(f"原始标签形状: {labels.shape}")
    print(f"增强标签形状: {aug_labels.shape}")
    print("数据增强测试完成")
