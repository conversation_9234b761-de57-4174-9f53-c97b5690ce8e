#!/usr/bin/env python3
"""
数据质量优化器 - 最后冲刺96%目标
"""

import cv2
import numpy as np
from PIL import Image
import json
import random
import shutil
from pathlib import Path
from typing import List, Dict, Tuple
import logging

# 添加训练模块路径
import sys
sys.path.append(str(Path(__file__).parent))

try:
    from advanced_ensemble import create_advanced_ensemble_recognizer
except ImportError:
    pass

logger = logging.getLogger(__name__)

class DataQualityOptimizer:
    """数据质量优化器"""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.quality_metrics = {}
        
    def analyze_image_quality(self, image_path: str) -> Dict:
        """分析单张图片质量"""
        try:
            # 加载图片
            img = cv2.imread(image_path)
            if img is None:
                return {'quality_score': 0, 'issues': ['无法加载图片']}
            
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 质量指标
            metrics = {}
            issues = []
            
            # 1. 清晰度 (Laplacian方差)
            clarity = cv2.Laplacian(gray, cv2.CV_64F).var()
            metrics['clarity'] = clarity
            if clarity < 100:
                issues.append('图片模糊')
            
            # 2. 亮度
            brightness = np.mean(gray)
            metrics['brightness'] = brightness
            if brightness < 50:
                issues.append('过暗')
            elif brightness > 200:
                issues.append('过亮')
            
            # 3. 对比度
            contrast = gray.std()
            metrics['contrast'] = contrast
            if contrast < 30:
                issues.append('对比度低')
            
            # 4. 图片尺寸
            height, width = img.shape[:2]
            metrics['size'] = height * width
            if height < 200 or width < 200:
                issues.append('分辨率过低')
            
            # 5. 噪声检测 (边缘密度)
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (height * width)
            metrics['edge_density'] = edge_density
            if edge_density > 0.3:
                issues.append('噪声过多')
            
            # 综合质量评分
            quality_score = 0
            if clarity >= 100: quality_score += 25
            if 80 <= brightness <= 180: quality_score += 25
            if contrast >= 30: quality_score += 25
            if height >= 300 and width >= 300: quality_score += 25
            
            return {
                'quality_score': quality_score,
                'metrics': metrics,
                'issues': issues
            }
            
        except Exception as e:
            return {'quality_score': 0, 'issues': [f'分析失败: {str(e)}']}
    
    def select_high_quality_images(self, cat_id: str, target_count: int = 8) -> List[str]:
        """为猫咪选择高质量图片"""
        cat_dir = self.dataset_path / cat_id
        if not cat_dir.exists():
            return []
        
        images = list(cat_dir.glob('*.jpg')) + list(cat_dir.glob('*.JPG'))
        
        # 分析所有图片质量
        image_qualities = []
        for img_path in images:
            quality = self.analyze_image_quality(str(img_path))
            image_qualities.append({
                'path': str(img_path),
                'quality_score': quality['quality_score'],
                'metrics': quality.get('metrics', {}),
                'issues': quality.get('issues', [])
            })
        
        # 按质量排序
        image_qualities.sort(key=lambda x: x['quality_score'], reverse=True)
        
        # 选择前target_count张高质量图片
        selected = image_qualities[:target_count]
        
        print(f"   猫咪 {cat_id}: 选择了{len(selected)}张高质量图片")
        for i, img in enumerate(selected[:3]):  # 显示前3张的质量
            print(f"     {Path(img['path']).name}: 质量={img['quality_score']}")
        
        return [img['path'] for img in selected]
    
    def create_optimized_dataset(self, target_cats: List[str]) -> Dict:
        """创建优化数据集"""
        print("🔧 创建优化数据集")
        print("-" * 40)
        
        optimized_data = {}
        
        for cat_id in target_cats:
            high_quality_images = self.select_high_quality_images(cat_id, target_count=10)
            if len(high_quality_images) >= 8:  # 至少需要8张高质量图片
                optimized_data[cat_id] = {
                    'registration_images': high_quality_images[:8],
                    'test_images': high_quality_images[8:] if len(high_quality_images) > 8 else high_quality_images[-2:],
                    'total_quality_images': len(high_quality_images)
                }
                print(f"   ✅ 猫咪 {cat_id}: {len(high_quality_images)}张高质量图片")
            else:
                print(f"   ❌ 猫咪 {cat_id}: 高质量图片不足({len(high_quality_images)})")
        
        return optimized_data

def test_with_optimized_data(num_rounds=3, cats_per_round=10):
    """使用优化数据进行测试"""
    print("🎯 数据质量优化测试")
    print("="*50)
    
    dataset_path = "/home/<USER>/animsi/caby_training/dataset/cat_individual_images"
    optimizer = DataQualityOptimizer(dataset_path)
    
    # 获取所有可用猫咪
    available_cats = []
    for cat_dir in Path(dataset_path).iterdir():
        if cat_dir.is_dir() and cat_dir.name.isdigit():
            images = list(cat_dir.glob('*.jpg')) + list(cat_dir.glob('*.JPG'))
            if len(images) >= 12:
                available_cats.append(cat_dir.name)
    
    all_results = []
    round_accuracies = []
    
    for round_num in range(1, num_rounds + 1):
        print(f"\n🔄 第 {round_num}/{num_rounds} 轮优化测试")
        
        # 随机选择猫咪
        test_cats = random.sample(available_cats, min(cats_per_round, len(available_cats)))
        print(f"   选中猫咪: {test_cats}")
        
        # 创建优化数据集
        optimized_data = optimizer.create_optimized_dataset(test_cats)
        
        if len(optimized_data) < 5:  # 至少需要5只猫有足够的高质量图片
            print(f"   ⚠️ 高质量图片不足，跳过本轮")
            continue
        
        # 清理数据库
        for db_name in ['optimized_enhanced_db', 'optimized_standard_db']:
            db_path = Path(db_name)
            if db_path.exists():
                shutil.rmtree(db_path)
        
        round_results = []
        round_correct = 0
        round_total = 0
        
        try:
            # 创建优化配置的集成系统
            config = {}
            ensemble = create_advanced_ensemble_recognizer(config)
            
            # 使用高质量图片注册
            print("   📝 使用高质量图片注册...")
            successful_registrations = 0
            
            for cat_id, data in optimized_data.items():
                result = ensemble.register_cat(
                    cat_id=cat_id,
                    cat_name=f"OptimizedCat_{cat_id}",
                    image_paths=data['registration_images']
                )
                
                if result.get('success', False):
                    successful_registrations += 1
                    success_models = result.get('successful_models', 0)
                    total_models = result.get('total_models', 0)
                    print(f"     ✅ 猫咪 {cat_id}: {success_models}/{total_models}模型成功")
            
            print(f"   成功注册: {successful_registrations}/{len(optimized_data)}")
            
            # 测试识别
            print("   🔍 优化数据识别测试...")
            for cat_id, data in optimized_data.items():
                expected_name = f"OptimizedCat_{cat_id}"
                
                # 使用测试图片或注册图片的后几张
                test_images = data.get('test_images', data['registration_images'][-4:])
                
                cat_correct = 0
                cat_total = 0
                
                for test_img_path in test_images:
                    result = ensemble.recognize_cat(
                        image_path=test_img_path,
                        return_alternatives=True,
                        confidence_threshold=None
                    )
                    
                    predicted_name = result.get('cat_name', 'Unknown')
                    confidence = result.get('confidence', 0.0)
                    status = result.get('status', 'unknown')
                    
                    is_correct = (predicted_name == expected_name and status == 'known')
                    
                    if is_correct:
                        cat_correct += 1
                        round_correct += 1
                    
                    cat_total += 1
                    round_total += 1
                    
                    round_results.append({
                        'round': round_num,
                        'cat_id': cat_id,
                        'expected': expected_name,
                        'predicted': predicted_name,
                        'confidence': confidence,
                        'status': status,
                        'correct': is_correct,
                        'image': Path(test_img_path).name,
                        'optimization': 'high_quality_data'
                    })
                
                cat_accuracy = cat_correct / cat_total if cat_total > 0 else 0
                status_icon = "✅" if cat_accuracy >= 0.96 else "❌"
                print(f"     猫咪 {cat_id}: {cat_accuracy:.1%} ({cat_correct}/{cat_total}) {status_icon}")
            
            ensemble.cleanup()
            
        except Exception as e:
            print(f"   ❌ 第{round_num}轮测试失败: {e}")
            continue
        
        round_accuracy = round_correct / round_total if round_total > 0 else 0
        round_accuracies.append(round_accuracy)
        all_results.extend(round_results)
        
        print(f"   📊 第{round_num}轮准确率: {round_accuracy:.1%} ({round_correct}/{round_total})")
        
        # 按猫咪统计
        cat_stats = {}
        for result in round_results:
            cat_id = result['cat_id']
            if cat_id not in cat_stats:
                cat_stats[cat_id] = {'total': 0, 'correct': 0}
            cat_stats[cat_id]['total'] += 1
            if result['correct']:
                cat_stats[cat_id]['correct'] += 1
        
        cats_above_96 = sum(1 for stats in cat_stats.values() 
                           if stats['correct'] / stats['total'] >= 0.96)
        print(f"   达到96%+的猫咪: {cats_above_96}/{len(cat_stats)}")
    
    # 计算总体统计
    if round_accuracies:
        avg_accuracy = sum(round_accuracies) / len(round_accuracies)
        min_accuracy = min(round_accuracies)
        max_accuracy = max(round_accuracies)
        
        print(f"\n📊 数据质量优化测试总结:")
        print(f"   平均准确率: {avg_accuracy:.1%}")
        print(f"   最低准确率: {min_accuracy:.1%}")
        print(f"   最高准确率: {max_accuracy:.1%}")
        print(f"   准确率范围: {min_accuracy:.1%} - {max_accuracy:.1%}")
        
        target_met = avg_accuracy >= 0.96
        print(f"   96%目标: {'✅ 达成' if target_met else '❌ 未达成'}")
        
        # 保存结果
        report = {
            'timestamp': str(Path().cwd()),
            'test_type': 'data_quality_optimization',
            'average_accuracy': avg_accuracy,
            'target_met': target_met,
            'detailed_results': all_results
        }
        
        filename = "data_quality_optimization_results.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📄 结果已保存: {filename}")
        
        return target_met, avg_accuracy
    
    return False, 0.0

if __name__ == "__main__":
    print("🚀 开始数据质量优化测试")
    
    success, avg_accuracy = test_with_optimized_data(num_rounds=3, cats_per_round=8)
    
    print(f"\n🎯 数据质量优化结果:")
    print(f"   平均准确率: {avg_accuracy:.1%}")
    print(f"   96%目标: {'✅ 达成' if success else '❌ 未达成'}")
    
    if success:
        print(f"\n🎉 恭喜！通过数据质量优化达到96%目标！")
    else:
        print(f"\n📈 数据质量优化完成，为进一步改进奠定基础")
    
    print("✅ 数据质量优化测试完成")
