#!/usr/bin/env python3
"""
特征向量数据库管理系统
支持猫咪特征向量的存储、检索、更新和管理
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import uuid
from datetime import datetime
import logging
import shutil

logger = logging.getLogger(__name__)

class FeatureDatabase:
    """特征向量数据库管理器"""
    
    def __init__(self, db_path: str = "feature_database"):
        """
        初始化特征数据库
        
        Args:
            db_path: 数据库存储路径
        """
        self.db_path = Path(db_path)
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # 数据库文件路径
        self.metadata_file = self.db_path / "metadata.json"
        self.features_dir = self.db_path / "features"
        self.features_dir.mkdir(exist_ok=True)
        
        # 加载元数据
        self.metadata = self._load_metadata()
        
        logger.info(f"特征数据库初始化完成: {self.db_path}")
        logger.info(f"当前包含 {len(self.metadata)} 只猫咪")
        
    def _load_metadata(self) -> Dict:
        """加载元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载元数据失败: {e}")
                return {}
        return {}
    
    def _save_metadata(self):
        """保存元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存元数据失败: {e}")
            raise
    
    def _generate_cat_id(self) -> str:
        """生成唯一的猫咪ID"""
        return str(uuid.uuid4())
    
    def _get_feature_file_path(self, cat_id: str) -> Path:
        """获取特征文件路径"""
        return self.features_dir / f"{cat_id}.npz"
    
    def add_cat(self, cat_name: str, cat_id: Optional[str] = None) -> str:
        """
        添加新猫咪
        
        Args:
            cat_name: 猫咪名称
            cat_id: 可选的猫咪ID，如果不提供则自动生成
            
        Returns:
            cat_id: 猫咪的唯一标识符
        """
        if cat_id is None:
            cat_id = self._generate_cat_id()
        
        # 检查名称是否已存在
        for existing_id, info in self.metadata.items():
            if info['cat_name'] == cat_name:
                logger.warning(f"猫咪名称 '{cat_name}' 已存在，ID: {existing_id}")
                return existing_id
        
        # 添加元数据
        self.metadata[cat_id] = {
            'cat_name': cat_name,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'feature_count': 0,
            'representative_feature_updated': False
        }
        
        self._save_metadata()
        logger.info(f"添加新猫咪: {cat_name} (ID: {cat_id})")
        return cat_id
    
    def add_feature(self, cat_id: str, feature_vector: np.ndarray, 
                   image_path: str, confidence: float = 1.0) -> bool:
        """
        为猫咪添加特征向量
        
        Args:
            cat_id: 猫咪ID
            feature_vector: 特征向量 (768,)
            image_path: 图像路径
            confidence: 置信度
            
        Returns:
            是否添加成功
        """
        if cat_id not in self.metadata:
            logger.error(f"猫咪ID不存在: {cat_id}")
            return False
        
        # 验证特征向量 - 支持768和1536维
        if not isinstance(feature_vector, np.ndarray):
            logger.error(f"特征向量类型错误: {type(feature_vector)}")
            return False

        # 支持多种特征维度
        valid_dims = [768, 1536]  # 支持原始和MegaDescriptor-L-384
        if len(feature_vector.shape) != 1 or feature_vector.shape[0] not in valid_dims:
            logger.error(f"特征向量维度错误: {feature_vector.shape}, 支持的维度: {valid_dims}")
            return False
        
        # 加载现有特征
        features_data = self._load_features(cat_id)
        
        # 添加新特征
        new_feature = {
            'feature_vector': feature_vector,
            'image_path': str(image_path),
            'timestamp': datetime.now().isoformat(),
            'confidence': confidence
        }
        
        features_data['features'].append(new_feature)
        
        # 限制特征数量（保留最新的50个）
        max_features = 50
        if len(features_data['features']) > max_features:
            features_data['features'] = features_data['features'][-max_features:]
        
        # 保存特征
        self._save_features(cat_id, features_data)
        
        # 更新元数据
        self.metadata[cat_id]['feature_count'] = len(features_data['features'])
        self.metadata[cat_id]['updated_at'] = datetime.now().isoformat()
        self.metadata[cat_id]['representative_feature_updated'] = False
        self._save_metadata()
        
        logger.info(f"为猫咪 {self.metadata[cat_id]['cat_name']} 添加特征向量")
        return True
    
    def _load_features(self, cat_id: str) -> Dict:
        """加载猫咪的特征数据"""
        feature_file = self._get_feature_file_path(cat_id)
        
        if feature_file.exists():
            try:
                data = np.load(feature_file, allow_pickle=True)
                return {
                    'features': data['features'].tolist(),
                    'representative_feature': data.get('representative_feature', None)
                }
            except Exception as e:
                logger.error(f"加载特征文件失败 {feature_file}: {e}")
        
        return {'features': [], 'representative_feature': None}
    
    def _save_features(self, cat_id: str, features_data: Dict):
        """保存猫咪的特征数据"""
        feature_file = self._get_feature_file_path(cat_id)
        
        try:
            # 准备保存的数据
            save_data = {
                'features': np.array(features_data['features'], dtype=object)
            }
            
            if features_data.get('representative_feature') is not None:
                save_data['representative_feature'] = features_data['representative_feature']
            
            np.savez_compressed(feature_file, **save_data)
            
        except Exception as e:
            logger.error(f"保存特征文件失败 {feature_file}: {e}")
            raise
    
    def get_representative_feature(self, cat_id: str) -> Optional[np.ndarray]:
        """
        获取猫咪的代表性特征向量
        
        Args:
            cat_id: 猫咪ID
            
        Returns:
            代表性特征向量或None
        """
        if cat_id not in self.metadata:
            return None
        
        features_data = self._load_features(cat_id)
        
        # 如果已有代表性特征且未过期，直接返回
        if (features_data.get('representative_feature') is not None and 
            self.metadata[cat_id].get('representative_feature_updated', False)):
            return features_data['representative_feature']
        
        # 计算代表性特征
        representative_feature = self._compute_representative_feature(features_data['features'])
        
        if representative_feature is not None:
            # 保存代表性特征
            features_data['representative_feature'] = representative_feature
            self._save_features(cat_id, features_data)
            
            # 更新元数据
            self.metadata[cat_id]['representative_feature_updated'] = True
            self._save_metadata()
        
        return representative_feature
    
    def _compute_representative_feature(self, features: List[Dict]) -> Optional[np.ndarray]:
        """
        计算代表性特征向量
        
        Args:
            features: 特征列表
            
        Returns:
            代表性特征向量
        """
        if not features:
            return None
        
        # 提取特征向量和置信度
        feature_vectors = []
        confidences = []
        
        for feat in features:
            feature_vectors.append(feat['feature_vector'])
            confidences.append(feat.get('confidence', 1.0))
        
        feature_vectors = np.array(feature_vectors)
        confidences = np.array(confidences)
        
        # 加权平均（根据置信度）
        weights = confidences / np.sum(confidences)
        representative = np.average(feature_vectors, axis=0, weights=weights)
        
        # L2归一化
        representative = representative / np.linalg.norm(representative)
        
        return representative
    
    def get_all_cats(self) -> List[Dict]:
        """获取所有猫咪信息"""
        cats = []
        for cat_id, info in self.metadata.items():
            cats.append({
                'cat_id': cat_id,
                'cat_name': info['cat_name'],
                'feature_count': info['feature_count'],
                'created_at': info['created_at'],
                'updated_at': info['updated_at']
            })
        return cats
    
    def get_cat_info(self, cat_id: str) -> Optional[Dict]:
        """获取特定猫咪信息"""
        if cat_id not in self.metadata:
            return None
        
        info = self.metadata[cat_id].copy()
        info['cat_id'] = cat_id
        return info
    
    def remove_cat(self, cat_id: str) -> bool:
        """
        删除猫咪及其所有数据
        
        Args:
            cat_id: 猫咪ID
            
        Returns:
            是否删除成功
        """
        if cat_id not in self.metadata:
            logger.error(f"猫咪ID不存在: {cat_id}")
            return False
        
        cat_name = self.metadata[cat_id]['cat_name']
        
        # 删除特征文件
        feature_file = self._get_feature_file_path(cat_id)
        if feature_file.exists():
            feature_file.unlink()
        
        # 删除元数据
        del self.metadata[cat_id]
        self._save_metadata()
        
        logger.info(f"删除猫咪: {cat_name} (ID: {cat_id})")
        return True
    
    def get_all_representative_features(self) -> Dict[str, np.ndarray]:
        """
        获取所有猫咪的代表性特征向量
        
        Returns:
            {cat_id: representative_feature} 字典
        """
        representatives = {}
        
        for cat_id in self.metadata.keys():
            rep_feature = self.get_representative_feature(cat_id)
            if rep_feature is not None:
                representatives[cat_id] = rep_feature
        
        return representatives
    
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份路径
            
        Returns:
            是否备份成功
        """
        try:
            backup_path = Path(backup_path)
            if backup_path.exists():
                shutil.rmtree(backup_path)
            
            shutil.copytree(self.db_path, backup_path)
            logger.info(f"数据库备份完成: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def get_cat_count(self) -> int:
        """获取已注册猫咪数量"""
        return len(self.metadata)

    def get_statistics(self) -> Dict:
        """获取数据库统计信息"""
        total_cats = len(self.metadata)
        total_features = sum(info['feature_count'] for info in self.metadata.values())
        
        feature_counts = [info['feature_count'] for info in self.metadata.values()]
        avg_features = np.mean(feature_counts) if feature_counts else 0
        
        return {
            'total_cats': total_cats,
            'total_features': total_features,
            'average_features_per_cat': avg_features,
            'database_size_mb': self._get_database_size_mb()
        }
    
    def _get_database_size_mb(self) -> float:
        """计算数据库大小（MB）"""
        total_size = 0
        for file_path in self.db_path.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        return total_size / (1024 * 1024)

class SimilarityCalculator:
    """相似度计算器"""

    @staticmethod
    def cosine_similarity(feat1: np.ndarray, feat2: np.ndarray) -> float:
        """
        计算两个特征向量的余弦相似度

        Args:
            feat1: 特征向量1
            feat2: 特征向量2

        Returns:
            余弦相似度 [0, 1]
        """
        # 对于L2归一化的向量，余弦相似度就是点积
        return float(np.dot(feat1, feat2))

    @staticmethod
    def batch_cosine_similarity(query: np.ndarray, database: np.ndarray) -> np.ndarray:
        """
        批量计算余弦相似度

        Args:
            query: 查询特征向量 (768,)
            database: 数据库特征向量矩阵 (N, 768)

        Returns:
            相似度数组 (N,)
        """
        # 对于L2归一化的向量，批量余弦相似度就是矩阵乘法
        return np.dot(database, query)

    @staticmethod
    def euclidean_distance(feat1: np.ndarray, feat2: np.ndarray) -> float:
        """
        计算欧氏距离

        Args:
            feat1: 特征向量1
            feat2: 特征向量2

        Returns:
            欧氏距离
        """
        return float(np.linalg.norm(feat1 - feat2))

    @staticmethod
    def batch_euclidean_distance(query: np.ndarray, database: np.ndarray) -> np.ndarray:
        """
        批量计算欧氏距离

        Args:
            query: 查询特征向量 (768,)
            database: 数据库特征向量矩阵 (N, 768)

        Returns:
            距离数组 (N,)
        """
        return np.linalg.norm(database - query, axis=1)

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)

    # 测试特征数据库
    print("测试特征数据库...")
    db = FeatureDatabase("test_feature_db")

    # 添加测试猫咪
    cat_id = db.add_cat("测试猫咪")

    # 添加测试特征
    test_feature = np.random.randn(768)
    test_feature = test_feature / np.linalg.norm(test_feature)  # L2归一化

    db.add_feature(cat_id, test_feature, "test_image.jpg", 0.95)

    # 获取代表性特征
    rep_feature = db.get_representative_feature(cat_id)
    print(f"代表性特征形状: {rep_feature.shape if rep_feature is not None else None}")

    # 获取统计信息
    stats = db.get_statistics()
    print(f"数据库统计: {stats}")

    # 测试相似度计算器
    print("\n测试相似度计算器...")
    calc = SimilarityCalculator()

    # 创建测试特征
    feat1 = np.random.randn(768)
    feat1 = feat1 / np.linalg.norm(feat1)

    feat2 = np.random.randn(768)
    feat2 = feat2 / np.linalg.norm(feat2)

    # 测试相似度计算
    cos_sim = calc.cosine_similarity(feat1, feat2)
    euc_dist = calc.euclidean_distance(feat1, feat2)

    print(f"余弦相似度: {cos_sim:.4f}")
    print(f"欧氏距离: {euc_dist:.4f}")

    # 测试批量计算
    database_features = np.random.randn(10, 768)
    database_features = database_features / np.linalg.norm(database_features, axis=1, keepdims=True)

    batch_similarities = calc.batch_cosine_similarity(feat1, database_features)
    batch_distances = calc.batch_euclidean_distance(feat1, database_features)

    print(f"批量相似度: {batch_similarities}")
    print(f"批量距离: {batch_distances}")

    print("测试完成！")
