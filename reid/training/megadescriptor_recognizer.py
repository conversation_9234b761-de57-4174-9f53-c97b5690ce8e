#!/usr/bin/env python3
"""
MegaDescriptor-L-384 识别器 - 使用大参数模型提升识别精度
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import timm
from torchvision import transforms
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from .vit_large_model import create_megadescriptor_large_model
    from .feature_database import FeatureDatabase
    from .matching_engine import MatchingEngine
except ImportError:
    from vit_large_model import create_megadescriptor_large_model
    from feature_database import FeatureDatabase
    from matching_engine import MatchingEngine

logger = logging.getLogger(__name__)

class MegaDescriptorRecognizer:
    """MegaDescriptor-L-384 猫咪识别器"""
    
    def __init__(self, config: Dict):
        """
        初始化MegaDescriptor识别器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型配置
        self.feature_dim = config.get('feature_dim', 1536)  # MegaDescriptor-L-384输出维度
        self.max_cats = config.get('max_cats', 100)
        self.confidence_threshold = config.get('confidence_threshold', 0.75)
        
        # 初始化组件
        self.model = None
        self.feature_db = None
        self.matching_engine = None
        self.transform = None
        
        # 性能统计
        self.stats = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'high_confidence_predictions': 0,
            'unknown_predictions': 0
        }
        
        self._initialize_components()
        
    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化MegaDescriptor-L-384模型
            logger.info("正在初始化MegaDescriptor-L-384模型...")
            self.model = create_megadescriptor_large_model(
                num_cats=self.max_cats,
                feature_dim=self.feature_dim,
                pretrained=True
            ).to(self.device)
            
            self.model.eval()
            logger.info("✅ MegaDescriptor-L-384模型初始化完成")
            
            # 初始化图像预处理 - 适配384x384输入
            self.transform = transforms.Compose([
                transforms.Resize((384, 384)),  # MegaDescriptor-L-384使用384x384
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            ])
            
            # 初始化特征数据库
            db_path = self.config.get('feature_db_path', 'megadescriptor_feature_db')
            self.feature_db = FeatureDatabase(db_path=db_path)
            
            # 初始化匹配引擎 - 使用更严格的阈值
            self.matching_engine = MatchingEngine(self.feature_db)
            
            # 更新匹配引擎的阈值配置
            self.matching_engine.thresholds.update({
                'high_confidence': 0.85,  # 提高高置信度阈值
                'medium_confidence': 0.75,
                'new_cat_threshold': 0.65,
                'min_samples_for_registration': 3
            })
            
            logger.info("✅ MegaDescriptor识别器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """
        注册新猫咪
        
        Args:
            cat_id: 猫咪ID
            cat_name: 猫咪名称
            image_paths: 图像路径列表
            
        Returns:
            注册结果
        """
        try:
            logger.info(f"注册猫咪: {cat_name} (ID: {cat_id})")
            
            # 提取特征向量
            features = []
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    feature = self._extract_single_feature(img_path)
                    if feature is not None:
                        features.append(feature)
                        valid_images += 1
                        logger.debug(f"成功提取特征: {Path(img_path).name}")
                except Exception as e:
                    logger.warning(f"处理图像 {img_path} 失败: {e}")
                    continue
            
            if len(features) < 3:
                return {
                    'success': False,
                    'error': f'有效图像数量不足: {len(features)}/3'
                }
            
            # 存储到特征数据库
            # 首先添加猫咪
            registered_cat_id = self.feature_db.add_cat(cat_name=cat_name, cat_id=cat_id)

            # 然后添加特征向量
            success_count = 0
            for i, feature in enumerate(features):
                success = self.feature_db.add_feature(
                    cat_id=registered_cat_id,
                    feature_vector=feature,
                    image_path=image_paths[i] if i < len(image_paths) else f"image_{i}",
                    confidence=1.0
                )
                if success:
                    success_count += 1

            if success_count > 0:
                logger.info(f"✅ 成功注册猫咪: {cat_name} ({success_count}个特征)")
                return {
                    'success': True,
                    'cat_id': registered_cat_id,
                    'cat_name': cat_name,
                    'features_count': success_count
                }
            else:
                return {
                    'success': False,
                    'error': '特征数据库存储失败'
                }
                
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str, return_alternatives: bool = False, 
                     confidence_threshold: Optional[float] = None) -> Dict:
        """
        识别猫咪
        
        Args:
            image_path: 图像路径
            return_alternatives: 是否返回候选结果
            confidence_threshold: 自定义置信度阈值
            
        Returns:
            识别结果
        """
        try:
            # 提取特征
            feature = self._extract_single_feature(image_path)
            if feature is None:
                return {
                    'status': 'error',
                    'cat_name': 'Unknown',
                    'confidence': 0.0,
                    'error': '特征提取失败'
                }
            
            # 使用自定义阈值 - MatchingEngine使用不同的阈值设置方式
            original_threshold = None
            if confidence_threshold is not None:
                # 暂时保存原始阈值配置
                original_threshold = getattr(self.matching_engine, 'thresholds', {}).copy()
                # 更新阈值
                if hasattr(self.matching_engine, 'thresholds'):
                    self.matching_engine.thresholds['high_confidence'] = confidence_threshold
            
            # 匹配
            match_result = self.matching_engine.match(
                query_feature=feature,
                return_alternatives=return_alternatives
            )
            
            # 恢复原始阈值
            if original_threshold is not None and hasattr(self.matching_engine, 'thresholds'):
                self.matching_engine.thresholds.update(original_threshold)
            
            # 更新统计
            self.stats['total_predictions'] += 1

            if match_result.status == 'known':
                if match_result.confidence > 0.9:
                    self.stats['high_confidence_predictions'] += 1
            else:
                self.stats['unknown_predictions'] += 1

            # 转换为字典格式并添加模型信息
            result_dict = {
                'status': match_result.status,
                'cat_name': match_result.cat_name or 'Unknown',
                'cat_id': match_result.cat_id,
                'confidence': match_result.confidence,
                'similarity_score': match_result.similarity_score,
                'match_type': match_result.match_type,
                'alternatives': match_result.alternatives,
                'processing_time': match_result.processing_time,
                'model_type': 'MegaDescriptor-L-384',
                'feature_dim': self.feature_dim
            }

            # 调试信息
            logger.debug(f"识别结果: 状态={result_dict['status']}, 猫咪={result_dict['cat_name']}, 置信度={result_dict['confidence']:.3f}")

            return result_dict
            
        except Exception as e:
            logger.error(f"识别失败: {e}")
            return {
                'status': 'error',
                'cat_name': 'Unknown',
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _extract_single_feature(self, image_path: str) -> Optional[np.ndarray]:
        """提取单张图像的特征向量"""
        try:
            # 加载和预处理图像
            image = Image.open(image_path).convert('RGB')
            input_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # 提取特征
            with torch.no_grad():
                features = self.model.extract_features(input_tensor)
                features = features.cpu().numpy().flatten()
            
            return features
            
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            return None
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        total = self.stats['total_predictions']
        accuracy = (self.stats['correct_predictions'] / total) if total > 0 else 0.0
        
        return {
            'registered_cats': self.feature_db.get_cat_count() if self.feature_db else 0,
            'total_predictions': total,
            'correct_predictions': self.stats['correct_predictions'],
            'accuracy': accuracy,
            'high_confidence_rate': (self.stats['high_confidence_predictions'] / total) if total > 0 else 0.0,
            'unknown_rate': (self.stats['unknown_predictions'] / total) if total > 0 else 0.0,
            'model_type': 'MegaDescriptor-L-384',
            'feature_dim': self.feature_dim,
            'input_size': '384x384'
        }
    
    def update_confidence_threshold(self, new_threshold: float):
        """更新置信度阈值"""
        self.confidence_threshold = new_threshold
        if self.matching_engine and hasattr(self.matching_engine, 'thresholds'):
            self.matching_engine.thresholds['high_confidence'] = new_threshold
        logger.info(f"置信度阈值已更新: {new_threshold}")
    
    def cleanup(self):
        """清理资源"""
        if self.feature_db:
            # FeatureDatabase没有close方法，只需要保存元数据
            try:
                self.feature_db._save_metadata()
            except:
                pass
        logger.info("MegaDescriptor识别器已清理")

def create_megadescriptor_recognizer(config: Dict) -> MegaDescriptorRecognizer:
    """创建MegaDescriptor识别器实例"""
    return MegaDescriptorRecognizer(config)

if __name__ == "__main__":
    # 测试MegaDescriptor识别器
    config = {
        'feature_dim': 1536,
        'max_cats': 50,
        'confidence_threshold': 0.75,
        'feature_db_path': 'test_megadescriptor_db'
    }
    
    recognizer = create_megadescriptor_recognizer(config)
    print("MegaDescriptor识别器创建成功")
    print(f"系统统计: {recognizer.get_system_stats()}")
    
    recognizer.cleanup()
