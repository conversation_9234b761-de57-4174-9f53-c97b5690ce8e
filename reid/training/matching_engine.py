#!/usr/bin/env python3
"""
匹配引擎 - 基于相似度的猫个体识别匹配算法
实现最近邻搜索、置信度评估和阈值判断
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from feature_database import FeatureDatabase, SimilarityCalculator

logger = logging.getLogger(__name__)

@dataclass
class MatchResult:
    """匹配结果数据类"""
    status: str  # "known", "unknown", "uncertain"
    cat_id: Optional[str]
    cat_name: Optional[str]
    confidence: float
    similarity_score: float
    match_type: str  # "high", "medium", "low"
    alternatives: List[Dict]
    processing_time: float

class MatchingEngine:
    """匹配引擎 - 核心匹配算法实现"""
    
    # 基于特征分析结果设定的阈值
    SIMILARITY_THRESHOLDS = {
        'high_confidence': 0.90,    # 高置信度匹配
        'medium_confidence': 0.80,  # 中等置信度匹配
        'new_cat_threshold': 0.70,  # 低于此值认为是新猫
        'min_samples_for_registration': 3  # 注册新猫需要的最少样本数
    }
    
    def __init__(self, feature_database: FeatureDatabase):
        """
        初始化匹配引擎

        Args:
            feature_database: 特征数据库实例
        """
        self.feature_db = feature_database
        self.similarity_calc = SimilarityCalculator()

        # 当前阈值（可动态调整）
        self.thresholds = self.SIMILARITY_THRESHOLDS.copy()

        # 缓存代表性特征以提高性能
        self._representative_features_cache = None
        self._cache_valid = False

        logger.info("匹配引擎初始化完成")
    
    def _update_cache(self):
        """更新代表性特征缓存"""
        representatives = self.feature_db.get_all_representative_features()
        
        if not representatives:
            self._representative_features_cache = None
            self._cache_valid = False
            return
        
        # 构建缓存数据结构
        cat_ids = list(representatives.keys())
        feature_matrix = np.array([representatives[cat_id] for cat_id in cat_ids])
        
        self._representative_features_cache = {
            'cat_ids': cat_ids,
            'features': feature_matrix,
            'cat_info': {cat_id: self.feature_db.get_cat_info(cat_id) for cat_id in cat_ids}
        }
        self._cache_valid = True
        
        logger.debug(f"缓存更新完成，包含 {len(cat_ids)} 只猫咪")
    
    def _ensure_cache_valid(self):
        """确保缓存有效"""
        if not self._cache_valid:
            self._update_cache()

    def update_thresholds(self, new_thresholds: Dict[str, float]):
        """
        更新匹配阈值

        Args:
            new_thresholds: 新的阈值字典
        """
        for key, value in new_thresholds.items():
            if key in self.thresholds:
                old_value = self.thresholds[key]
                self.thresholds[key] = value
                logger.info(f"阈值更新: {key} {old_value:.3f} -> {value:.3f}")

    def match(self, query_feature: np.ndarray, return_alternatives: bool = True,
              max_alternatives: int = 3) -> MatchResult:
        """
        执行特征匹配
        
        Args:
            query_feature: 查询特征向量 (768,)
            return_alternatives: 是否返回备选匹配
            max_alternatives: 最大备选数量
            
        Returns:
            匹配结果
        """
        import time
        start_time = time.time()
        
        # 确保缓存有效
        self._ensure_cache_valid()
        
        # 如果数据库为空
        if self._representative_features_cache is None:
            return MatchResult(
                status="unknown",
                cat_id=None,
                cat_name=None,
                confidence=0.0,
                similarity_score=0.0,
                match_type="low",
                alternatives=[],
                processing_time=time.time() - start_time
            )
        
        # 批量计算相似度
        similarities = self.similarity_calc.batch_cosine_similarity(
            query_feature, 
            self._representative_features_cache['features']
        )
        
        # 排序获取最佳匹配
        sorted_indices = np.argsort(similarities)[::-1]  # 降序排列
        best_idx = sorted_indices[0]
        best_similarity = similarities[best_idx]
        best_cat_id = self._representative_features_cache['cat_ids'][best_idx]
        best_cat_info = self._representative_features_cache['cat_info'][best_cat_id]
        
        # 判断匹配类型和状态
        match_type, status, confidence = self._evaluate_match(best_similarity)
        
        # 准备备选匹配
        alternatives = []
        if return_alternatives and len(sorted_indices) > 1:
            for i in range(1, min(len(sorted_indices), max_alternatives + 1)):
                alt_idx = sorted_indices[i]
                alt_similarity = similarities[alt_idx]
                alt_cat_id = self._representative_features_cache['cat_ids'][alt_idx]
                alt_cat_info = self._representative_features_cache['cat_info'][alt_cat_id]
                
                # 只包含有意义的备选项
                if alt_similarity > self.thresholds['new_cat_threshold']:
                    alternatives.append({
                        'cat_id': alt_cat_id,
                        'cat_name': alt_cat_info['cat_name'],
                        'similarity_score': float(alt_similarity)
                    })
        
        return MatchResult(
            status=status,
            cat_id=best_cat_id if status != "unknown" else None,
            cat_name=best_cat_info['cat_name'] if status != "unknown" else None,
            confidence=confidence,
            similarity_score=float(best_similarity),
            match_type=match_type,
            alternatives=alternatives,
            processing_time=time.time() - start_time
        )
    
    def _evaluate_match(self, similarity: float) -> Tuple[str, str, float]:
        """
        评估匹配质量
        
        Args:
            similarity: 相似度分数
            
        Returns:
            (match_type, status, confidence)
        """
        if similarity >= self.thresholds['high_confidence']:
            return "high", "known", self._similarity_to_confidence(similarity, "high")
        elif similarity >= self.thresholds['medium_confidence']:
            return "medium", "known", self._similarity_to_confidence(similarity, "medium")
        elif similarity >= self.thresholds['new_cat_threshold']:
            return "low", "uncertain", self._similarity_to_confidence(similarity, "low")
        else:
            return "low", "unknown", 0.0
    
    def _similarity_to_confidence(self, similarity: float, match_type: str) -> float:
        """
        将相似度转换为置信度

        Args:
            similarity: 相似度分数
            match_type: 匹配类型

        Returns:
            置信度 [0, 1]
        """
        high_thresh = self.thresholds['high_confidence']
        medium_thresh = self.thresholds['medium_confidence']
        low_thresh = self.thresholds['new_cat_threshold']

        if match_type == "high":
            # 高置信度区间 [high_thresh, 1.0] -> [0.85, 1.0]
            range_size = 1.0 - high_thresh
            if range_size > 0:
                return 0.85 + (similarity - high_thresh) / range_size * 0.15
            else:
                return 0.95
        elif match_type == "medium":
            # 中等置信度区间 [medium_thresh, high_thresh] -> [0.60, 0.85]
            range_size = high_thresh - medium_thresh
            if range_size > 0:
                return 0.60 + (similarity - medium_thresh) / range_size * 0.25
            else:
                return 0.72
        else:  # low
            # 低置信度区间 [low_thresh, medium_thresh] -> [0.30, 0.60]
            range_size = medium_thresh - low_thresh
            if range_size > 0:
                return 0.30 + (similarity - low_thresh) / range_size * 0.30
            else:
                return 0.45
    
    def batch_match(self, query_features: np.ndarray, **kwargs) -> List[MatchResult]:
        """
        批量匹配
        
        Args:
            query_features: 查询特征矩阵 (N, 768)
            **kwargs: 传递给match方法的参数
            
        Returns:
            匹配结果列表
        """
        results = []
        for i in range(query_features.shape[0]):
            result = self.match(query_features[i], **kwargs)
            results.append(result)
        return results
    
    def get_match_statistics(self) -> Dict:
        """获取匹配统计信息"""
        self._ensure_cache_valid()
        
        if self._representative_features_cache is None:
            return {
                'total_cats': 0,
                'average_inter_similarity': 0.0,
                'similarity_distribution': {}
            }
        
        features = self._representative_features_cache['features']
        n_cats = len(features)
        
        # 计算类间相似度统计
        if n_cats > 1:
            similarities = []
            for i in range(n_cats):
                for j in range(i + 1, n_cats):
                    sim = self.similarity_calc.cosine_similarity(features[i], features[j])
                    similarities.append(sim)
            
            avg_inter_sim = np.mean(similarities)
            
            # 相似度分布统计
            high_sim = sum(1 for s in similarities if s >= self.SIMILARITY_THRESHOLDS['high_confidence'])
            medium_sim = sum(1 for s in similarities if self.SIMILARITY_THRESHOLDS['medium_confidence'] <= s < self.SIMILARITY_THRESHOLDS['high_confidence'])
            low_sim = sum(1 for s in similarities if s < self.SIMILARITY_THRESHOLDS['medium_confidence'])
            
            similarity_distribution = {
                'high': high_sim,
                'medium': medium_sim,
                'low': low_sim
            }
        else:
            avg_inter_sim = 0.0
            similarity_distribution = {'high': 0, 'medium': 0, 'low': 0}
        
        return {
            'total_cats': n_cats,
            'average_inter_similarity': avg_inter_sim,
            'similarity_distribution': similarity_distribution
        }
    
    def invalidate_cache(self):
        """使缓存失效，强制下次更新"""
        self._cache_valid = False
        logger.debug("匹配引擎缓存已失效")
    
    def update_thresholds(self, new_thresholds: Dict[str, float]):
        """
        更新匹配阈值
        
        Args:
            new_thresholds: 新的阈值字典
        """
        for key, value in new_thresholds.items():
            if key in self.SIMILARITY_THRESHOLDS:
                old_value = self.SIMILARITY_THRESHOLDS[key]
                self.SIMILARITY_THRESHOLDS[key] = value
                logger.info(f"阈值更新: {key} {old_value:.3f} -> {value:.3f}")
    
    def optimize_thresholds(self, validation_data: List[Tuple[np.ndarray, str]]) -> Dict[str, float]:
        """
        基于验证数据优化阈值
        
        Args:
            validation_data: [(feature_vector, true_cat_id), ...]
            
        Returns:
            优化后的阈值
        """
        # 这里可以实现基于验证数据的阈值优化算法
        # 暂时返回当前阈值
        logger.info("阈值优化功能待实现")
        return self.SIMILARITY_THRESHOLDS.copy()

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    from feature_database import FeatureDatabase
    
    # 创建测试环境
    db = FeatureDatabase("test_matching_db")
    engine = MatchingEngine(db)
    
    # 添加测试猫咪
    cat1_id = db.add_cat("小白")
    cat2_id = db.add_cat("小花")
    
    # 添加特征向量
    for i in range(5):
        feature1 = np.random.randn(768)
        feature1 = feature1 / np.linalg.norm(feature1)
        db.add_feature(cat1_id, feature1, f"cat1_image_{i}.jpg", 0.9)
        
        feature2 = np.random.randn(768)
        feature2 = feature2 / np.linalg.norm(feature2)
        db.add_feature(cat2_id, feature2, f"cat2_image_{i}.jpg", 0.9)
    
    # 测试匹配
    query_feature = np.random.randn(768)
    query_feature = query_feature / np.linalg.norm(query_feature)
    
    result = engine.match(query_feature)
    print(f"匹配结果: {result}")
    
    # 获取统计信息
    stats = engine.get_match_statistics()
    print(f"匹配统计: {stats}")
    
    print("匹配引擎测试完成！")
