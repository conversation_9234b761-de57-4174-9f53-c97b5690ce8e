#!/usr/bin/env python3
"""
对比学习训练模块 - 专门解决相似猫咪混淆问题
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import random
from pathlib import Path
import logging
from typing import List, Tuple, Dict
import timm
from torchvision import transforms

logger = logging.getLogger(__name__)

class TripletLoss(nn.Module):
    """Triplet Loss for contrastive learning"""
    
    def __init__(self, margin=0.3):
        super(TripletLoss, self).__init__()
        self.margin = margin
        
    def forward(self, anchor, positive, negative):
        """
        Args:
            anchor: 锚点特征 [batch_size, feature_dim]
            positive: 正样本特征 [batch_size, feature_dim]
            negative: 负样本特征 [batch_size, feature_dim]
        """
        pos_dist = F.pairwise_distance(anchor, positive, p=2)
        neg_dist = F.pairwise_distance(anchor, negative, p=2)
        
        loss = F.relu(pos_dist - neg_dist + self.margin)
        return loss.mean()

class ContrastiveLoss(nn.Module):
    """Contrastive Loss for pair-wise learning"""
    
    def __init__(self, margin=1.0):
        super(ContrastiveLoss, self).__init__()
        self.margin = margin
        
    def forward(self, feature1, feature2, label):
        """
        Args:
            feature1, feature2: 特征向量 [batch_size, feature_dim]
            label: 1 if same cat, 0 if different cats
        """
        distance = F.pairwise_distance(feature1, feature2, p=2)
        
        # 相同猫咪：距离应该小
        pos_loss = label * torch.pow(distance, 2)
        
        # 不同猫咪：距离应该大于margin
        neg_loss = (1 - label) * torch.pow(F.relu(self.margin - distance), 2)
        
        loss = 0.5 * (pos_loss + neg_loss)
        return loss.mean()

class CatTripletDataset(Dataset):
    """猫咪三元组数据集"""
    
    def __init__(self, dataset_path: str, transform=None, samples_per_cat=50):
        self.dataset_path = Path(dataset_path)
        self.transform = transform
        self.samples_per_cat = samples_per_cat
        
        # 收集所有猫咪的图片
        self.cat_images = {}
        self._collect_images()
        
        # 生成三元组
        self.triplets = self._generate_triplets()
        
        logger.info(f"创建三元组数据集: {len(self.triplets)}个三元组, {len(self.cat_images)}只猫")
    
    def _collect_images(self):
        """收集所有猫咪的图片"""
        for cat_dir in self.dataset_path.iterdir():
            if cat_dir.is_dir() and cat_dir.name.isdigit():
                images = list(cat_dir.glob('*.jpg')) + list(cat_dir.glob('*.JPG'))
                if len(images) >= 8:  # 至少8张图片
                    self.cat_images[cat_dir.name] = images[:20]  # 最多20张
    
    def _generate_triplets(self):
        """生成三元组 (anchor, positive, negative)"""
        triplets = []
        cat_ids = list(self.cat_images.keys())
        
        for cat_id in cat_ids:
            images = self.cat_images[cat_id]
            
            # 为每只猫生成指定数量的三元组
            for _ in range(self.samples_per_cat):
                if len(images) < 2:
                    continue
                
                # 选择anchor和positive（同一只猫的不同图片）
                anchor_img, positive_img = random.sample(images, 2)
                
                # 选择negative（不同猫的图片）
                other_cats = [cid for cid in cat_ids if cid != cat_id]
                if not other_cats:
                    continue
                
                negative_cat = random.choice(other_cats)
                negative_img = random.choice(self.cat_images[negative_cat])
                
                triplets.append({
                    'anchor': anchor_img,
                    'positive': positive_img,
                    'negative': negative_img,
                    'anchor_cat': cat_id,
                    'negative_cat': negative_cat
                })
        
        return triplets
    
    def __len__(self):
        return len(self.triplets)
    
    def __getitem__(self, idx):
        triplet = self.triplets[idx]
        
        # 加载图片
        anchor = Image.open(triplet['anchor']).convert('RGB')
        positive = Image.open(triplet['positive']).convert('RGB')
        negative = Image.open(triplet['negative']).convert('RGB')
        
        # 应用变换
        if self.transform:
            anchor = self.transform(anchor)
            positive = self.transform(positive)
            negative = self.transform(negative)
        
        return {
            'anchor': anchor,
            'positive': positive,
            'negative': negative,
            'anchor_cat': triplet['anchor_cat'],
            'negative_cat': triplet['negative_cat']
        }

class ContrastiveModel(nn.Module):
    """对比学习模型 - 内存优化版"""

    def __init__(self, backbone='vit_base_patch16_224', feature_dim=768, dropout=0.1):
        super(ContrastiveModel, self).__init__()

        # 使用更小的ViT模型以节省内存
        self.backbone = timm.create_model(
            backbone,
            pretrained=True,
            num_classes=0,  # 移除分类头
            global_pool='avg'
        )
        
        # 获取backbone输出维度 - 使用更小的输入尺寸
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_dim = self.backbone(dummy_input).shape[1]
        
        # 简化的投影头以节省内存
        self.projection = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(feature_dim, feature_dim)
        )
        
        # L2归一化
        self.normalize = nn.functional.normalize
        
    def forward(self, x):
        """前向传播"""
        # 提取特征
        features = self.backbone(x)
        
        # 投影
        projected = self.projection(features)
        
        # L2归一化
        normalized = self.normalize(projected, p=2, dim=1)
        
        return normalized

class ContrastiveLearningTrainer:
    """对比学习训练器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型
        self.model = ContrastiveModel(
            backbone=config.get('backbone', 'vit_large_patch16_384'),
            feature_dim=config.get('feature_dim', 1536),
            dropout=config.get('dropout', 0.1)
        ).to(self.device)
        
        # 损失函数
        self.triplet_loss = TripletLoss(margin=config.get('triplet_margin', 0.3))
        self.contrastive_loss = ContrastiveLoss(margin=config.get('contrastive_margin', 1.0))
        
        # 优化器
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=config.get('learning_rate', 1e-4),
            weight_decay=config.get('weight_decay', 1e-4)
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=config.get('epochs', 50),
            eta_min=1e-6
        )
        
        # 数据变换 - 使用更小的图像尺寸
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),  # 减小图像尺寸
            transforms.RandomHorizontalFlip(0.5),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.1, contrast=0.1, saturation=0.1),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
        
        logger.info("对比学习训练器初始化完成")
    
    def train(self, dataset_path: str, epochs: int = 50, batch_size: int = 16):
        """训练对比学习模型"""
        logger.info(f"开始对比学习训练: {epochs}轮, batch_size={batch_size}")
        
        # 创建数据集
        dataset = CatTripletDataset(
            dataset_path=dataset_path,
            transform=self.transform,
            samples_per_cat=self.config.get('samples_per_cat', 50)
        )
        
        dataloader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )
        
        self.model.train()
        
        for epoch in range(epochs):
            total_loss = 0.0
            triplet_losses = []
            
            for batch_idx, batch in enumerate(dataloader):
                # 移动到设备
                anchor = batch['anchor'].to(self.device)
                positive = batch['positive'].to(self.device)
                negative = batch['negative'].to(self.device)
                
                # 前向传播
                anchor_features = self.model(anchor)
                positive_features = self.model(positive)
                negative_features = self.model(negative)
                
                # 计算triplet loss
                triplet_loss = self.triplet_loss(
                    anchor_features, positive_features, negative_features
                )
                
                # 反向传播
                self.optimizer.zero_grad()
                triplet_loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                self.optimizer.step()
                
                total_loss += triplet_loss.item()
                triplet_losses.append(triplet_loss.item())
                
                if batch_idx % 50 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Batch {batch_idx}, "
                              f"Triplet Loss: {triplet_loss.item():.4f}")
            
            # 更新学习率
            self.scheduler.step()
            
            avg_loss = total_loss / len(dataloader)
            logger.info(f"Epoch {epoch+1}/{epochs} 完成, 平均损失: {avg_loss:.4f}")
            
            # 每10轮保存一次模型
            if (epoch + 1) % 10 == 0:
                self.save_model(f"contrastive_model_epoch_{epoch+1}.pth")
        
        logger.info("对比学习训练完成")
        return self.model
    
    def save_model(self, filename: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config
        }, filename)
        logger.info(f"模型已保存: {filename}")
    
    def load_model(self, filename: str):
        """加载模型"""
        checkpoint = torch.load(filename, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"模型已加载: {filename}")

def train_contrastive_model():
    """训练对比学习模型 - 内存优化版"""
    config = {
        'backbone': 'vit_base_patch16_224',  # 使用更小的模型
        'feature_dim': 768,  # 减小特征维度
        'dropout': 0.1,
        'triplet_margin': 0.3,
        'contrastive_margin': 1.0,
        'learning_rate': 1e-4,
        'weight_decay': 1e-4,
        'epochs': 20,  # 减少训练轮数
        'samples_per_cat': 20  # 减少每只猫的样本数
    }
    
    trainer = ContrastiveLearningTrainer(config)
    
    dataset_path = "/home/<USER>/animsi/caby_training/dataset/cat_individual_images"
    
    # 训练模型 - 使用更小的batch size
    model = trainer.train(
        dataset_path=dataset_path,
        epochs=config['epochs'],
        batch_size=4  # 进一步减小batch size
    )
    
    # 保存最终模型
    trainer.save_model("contrastive_cat_model_final.pth")
    
    return model

if __name__ == "__main__":
    print("🚀 开始对比学习训练")
    model = train_contrastive_model()
    print("✅ 对比学习训练完成")
