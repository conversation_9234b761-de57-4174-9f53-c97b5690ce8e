#!/usr/bin/env python3
"""
特征增强训练 - 专门提升MegaDescriptor的猫咪特征区分能力
通过对比学习和三元组损失进行续训
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CatTripletDataset(Dataset):
    """猫咪三元组数据集 - 专门用于提升特征区分度"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100):
        self.dataset_path = Path(dataset_path)
        self.max_cats = max_cats
        
        # 数据增强 - 更强的增强来提升泛化能力
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=20),
            transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),
            transforms.RandomAffine(degrees=0, translate=(0.15, 0.15), scale=(0.85, 1.15)),
            transforms.RandomPerspective(distortion_scale=0.1, p=0.3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 构建数据集
        self.cat_images = defaultdict(list)
        self.cat_ids = []
        
        self._build_dataset()
        
        logger.info(f"三元组数据集构建完成: {len(self.cat_ids)} 只猫咪, "
                   f"总图片数: {sum(len(imgs) for imgs in self.cat_images.values())}")
    
    def _build_dataset(self):
        """构建数据集"""
        # 获取所有猫咪文件夹
        cat_folders = [f for f in self.dataset_path.iterdir() 
                      if f.is_dir() and f.name.isdigit()]
        
        # 过滤图片数量不足的猫咪
        valid_cats = []
        for cat_folder in cat_folders:
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:  # 至少5张图片
                valid_cats.append((cat_folder.name, images))
        
        # 按图片数量排序，选择数据丰富的猫咪
        valid_cats.sort(key=lambda x: len(x[1]), reverse=True)
        selected_cats = valid_cats[:self.max_cats]
        
        # 构建猫咪图片字典
        for cat_id, images in selected_cats:
            self.cat_images[cat_id] = [str(img) for img in images]
            self.cat_ids.append(cat_id)
    
    def __len__(self):
        # 每个epoch生成大量三元组
        return len(self.cat_ids) * 50
    
    def __getitem__(self, idx):
        """生成三元组: (anchor, positive, negative)"""
        # 随机选择anchor猫咪
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择positive (同一只猫的不同图片)
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 确保positive和anchor不是同一张图片
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative (不同猫咪的图片)
        negative_cat = random.choice([cat for cat in self.cat_ids if cat != anchor_cat])
        negative_img = random.choice(self.cat_images[negative_cat])
        
        # 加载和转换图片
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative, anchor_cat, negative_cat
        except Exception as e:
            logger.warning(f"加载图片失败: {e}")
            # 递归重试
            return self.__getitem__(random.randint(0, len(self) - 1))

class EnhancedMegaDescriptor(nn.Module):
    """增强版MegaDescriptor - 添加特征增强层"""
    
    def __init__(self, feature_dim=512):
        super().__init__()
        
        # 加载预训练的MegaDescriptor
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0  # 移除分类头
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
        
        # L2归一化层
        self.l2_norm = nn.functional.normalize
        
        logger.info(f"增强版MegaDescriptor初始化完成: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = self.l2_norm(enhanced_features, p=2, dim=1)
        
        return normalized_features

class TripletLoss(nn.Module):
    """三元组损失 - 专门提升特征区分度"""
    
    def __init__(self, margin=0.5):
        super().__init__()
        self.margin = margin
        
    def forward(self, anchor, positive, negative):
        # 计算距离
        pos_dist = torch.norm(anchor - positive, p=2, dim=1)
        neg_dist = torch.norm(anchor - negative, p=2, dim=1)
        
        # 三元组损失
        loss = torch.clamp(pos_dist - neg_dist + self.margin, min=0.0)
        
        return loss.mean()

class FeatureEnhancementTrainer:
    """特征增强训练器"""
    
    def __init__(self, dataset_path: str, max_cats: int = 100, device='cuda'):
        self.dataset_path = dataset_path
        self.max_cats = max_cats
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 创建数据集和数据加载器
        self.dataset = CatTripletDataset(dataset_path, max_cats)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=16,  # 较小的batch size以适应三元组
            shuffle=True, 
            num_workers=4,
            pin_memory=True
        )
        
        # 创建模型
        self.model = EnhancedMegaDescriptor(feature_dim=512).to(self.device)
        
        # 损失函数
        self.triplet_loss = TripletLoss(margin=0.5)
        
        # 优化器 - 使用较小的学习率进行微调
        self.optimizer = optim.AdamW([
            {'params': self.model.backbone.parameters(), 'lr': 1e-5},  # 骨干网络用更小学习率
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-3}  # 新增层用正常学习率
        ], weight_decay=1e-4)
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=20, eta_min=1e-6
        )
        
        logger.info(f"特征增强训练器初始化完成: {len(self.dataset.cat_ids)} 只猫咪")
    
    def train_epoch(self, epoch: int) -> Dict:
        """训练一个epoch"""
        self.model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative, anchor_cats, negative_cats) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_features = self.model(anchor)
            positive_features = self.model(positive)
            negative_features = self.model(negative)
            
            # 计算三元组损失
            loss = self.triplet_loss(anchor_features, positive_features, negative_features)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 打印进度
            if batch_idx % 50 == 0:
                logger.info(f"Epoch {epoch}, Batch {batch_idx}/{len(self.dataloader)}: "
                          f"Loss={loss.item():.4f}")
        
        # 更新学习率
        self.scheduler.step()
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        return {
            'loss': avg_loss,
            'learning_rate': self.scheduler.get_last_lr()[0]
        }
    
    def evaluate_model(self) -> Dict:
        """评估模型特征质量"""
        self.model.eval()
        
        # 随机选择一些猫咪进行评估
        eval_cats = random.sample(self.dataset.cat_ids, min(10, len(self.dataset.cat_ids)))
        
        intra_distances = []  # 类内距离
        inter_distances = []  # 类间距离
        
        with torch.no_grad():
            for cat_id in eval_cats:
                cat_images = self.dataset.cat_images[cat_id]
                
                # 随机选择2张图片计算类内距离
                if len(cat_images) >= 2:
                    img1_path, img2_path = random.sample(cat_images, 2)
                    
                    img1 = self.dataset.transform(Image.open(img1_path).convert('RGB')).unsqueeze(0).to(self.device)
                    img2 = self.dataset.transform(Image.open(img2_path).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1 = self.model(img1)
                    feat2 = self.model(img2)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_distances.append(intra_dist)
                
                # 计算与其他猫咪的类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img_path = random.choice(self.dataset.cat_images[other_cat])
                
                img1 = self.dataset.transform(Image.open(random.choice(cat_images)).convert('RGB')).unsqueeze(0).to(self.device)
                img2 = self.dataset.transform(Image.open(other_img_path).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1 = self.model(img1)
                feat2 = self.model(img2)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_distances.append(inter_dist)
        
        avg_intra_dist = np.mean(intra_distances) if intra_distances else 0.0
        avg_inter_dist = np.mean(inter_distances) if inter_distances else 0.0
        
        # 分离度指标：类间距离 / 类内距离
        separability = avg_inter_dist / (avg_intra_dist + 1e-8)
        
        return {
            'avg_intra_distance': avg_intra_dist,
            'avg_inter_distance': avg_inter_dist,
            'separability': separability
        }
    
    def train(self, epochs: int = 10, save_path: str = 'enhanced_megadescriptor.pth') -> Dict:
        """完整训练流程"""
        logger.info(f"开始特征增强训练: {epochs} epochs")
        
        training_history = {
            'epochs': [],
            'losses': [],
            'separabilities': []
        }
        
        best_separability = 0.0
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练一个epoch
            train_stats = self.train_epoch(epoch + 1)
            
            # 评估模型
            eval_stats = self.evaluate_model()
            
            # 记录历史
            training_history['epochs'].append(epoch + 1)
            training_history['losses'].append(train_stats['loss'])
            training_history['separabilities'].append(eval_stats['separability'])
            
            epoch_time = time.time() - start_time
            
            logger.info(f"Epoch {epoch + 1}/{epochs} 完成:")
            logger.info(f"  损失: {train_stats['loss']:.4f}")
            logger.info(f"  类内距离: {eval_stats['avg_intra_distance']:.4f}")
            logger.info(f"  类间距离: {eval_stats['avg_inter_distance']:.4f}")
            logger.info(f"  分离度: {eval_stats['separability']:.4f}")
            logger.info(f"  学习率: {train_stats['learning_rate']:.2e}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if eval_stats['separability'] > best_separability:
                best_separability = eval_stats['separability']
                self.save_model(save_path)
                logger.info(f"保存最佳模型: 分离度 {best_separability:.4f}")
        
        logger.info(f"训练完成! 最佳分离度: {best_separability:.4f}")
        
        return training_history
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'cat_ids': self.dataset.cat_ids,
            'num_cats': len(self.dataset.cat_ids)
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='特征增强训练')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--max-cats', type=int, default=100,
                       help='最大猫咪数量')
    parser.add_argument('--epochs', type=int, default=10,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='enhanced_megadescriptor.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = FeatureEnhancementTrainer(
        dataset_path=args.dataset,
        max_cats=args.max_cats
    )
    
    # 开始训练
    history = trainer.train(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"特征增强训练完成，模型已保存: {args.output}")

if __name__ == "__main__":
    main()
