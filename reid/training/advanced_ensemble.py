#!/usr/bin/env python3
"""
高级集成系统 - 多策略组合提升到96%
"""

import torch
import numpy as np
from pathlib import Path
import json
import random
import time
from typing import Dict, List, Optional, Tuple
import logging

# 添加训练模块路径
import sys
sys.path.append(str(Path(__file__).parent))

try:
    from enhanced_megadescriptor import create_enhanced_megadescriptor_recognizer
    from megadescriptor_recognizer import create_megadescriptor_recognizer
except ImportError:
    pass

logger = logging.getLogger(__name__)

class AdvancedEnsembleRecognizer:
    """高级集成识别器 - 多策略组合"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.models = {}
        self.model_weights = {}
        self.adaptive_thresholds = {}
        
        # 性能统计
        self.stats = {
            'total_predictions': 0,
            'ensemble_improvements': 0,
            'threshold_adjustments': 0,
            'model_agreements': 0
        }
        
        self._initialize_ensemble()
    
    def _initialize_ensemble(self):
        """初始化集成模型"""
        print("🚀 初始化高级集成系统")
        
        try:
            # 模型1: 增强版MegaDescriptor (主力)
            config1 = {
                'feature_dim': 1536,
                'max_cats': 50,
                'confidence_threshold': 0.45,  # 较低阈值
                'feature_db_path': 'ensemble_enhanced_db',
                'enable_tta': True,
                'enable_feature_ensemble': True,
                'enable_adaptive_threshold': True
            }
            self.models['enhanced'] = create_enhanced_megadescriptor_recognizer(config1)
            self.model_weights['enhanced'] = 0.6
            print("   ✅ 增强版MegaDescriptor初始化完成")
            
            # 模型2: 标准MegaDescriptor (辅助)
            config2 = {
                'feature_dim': 1536,
                'max_cats': 50,
                'confidence_threshold': 0.50,
                'feature_db_path': 'ensemble_standard_db'
            }
            self.models['standard'] = create_megadescriptor_recognizer(config2)
            self.model_weights['standard'] = 0.4
            print("   ✅ 标准MegaDescriptor初始化完成")
            
            # 自适应阈值
            self.adaptive_thresholds = {
                'high_confidence': 0.85,
                'medium_confidence': 0.65,
                'low_confidence': 0.45,
                'ensemble_boost': 0.1  # 集成时的置信度提升
            }
            
            print("   ✅ 高级集成系统初始化完成")
            
        except Exception as e:
            logger.error(f"集成系统初始化失败: {e}")
            raise
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """在所有模型中注册猫咪"""
        results = {}
        success_count = 0
        
        for model_name, model in self.models.items():
            try:
                result = model.register_cat(cat_id, cat_name, image_paths)
                results[model_name] = result
                
                if result.get('success', False):
                    success_count += 1
                    print(f"     ✅ {model_name}: {result.get('features_count', 0)}个特征")
                else:
                    print(f"     ❌ {model_name}: {result.get('error', 'Unknown')}")
                    
            except Exception as e:
                logger.error(f"{model_name}模型注册异常: {e}")
                results[model_name] = {'success': False, 'error': str(e)}
        
        overall_success = success_count > 0
        
        return {
            'success': overall_success,
            'cat_id': cat_id,
            'cat_name': cat_name,
            'successful_models': success_count,
            'total_models': len(self.models),
            'model_results': results
        }
    
    def recognize_cat(self, image_path: str, return_alternatives: bool = False,
                     confidence_threshold: Optional[float] = None) -> Dict:
        """高级集成识别"""
        try:
            # 获取所有模型的预测
            model_predictions = {}
            valid_predictions = []
            
            for model_name, model in self.models.items():
                try:
                    result = model.recognize_cat(
                        image_path=image_path,
                        return_alternatives=return_alternatives,
                        confidence_threshold=None  # 使用模型默认阈值
                    )
                    
                    model_predictions[model_name] = result
                    
                    # 收集有效预测
                    if result.get('status') == 'known' and result.get('confidence', 0) > 0:
                        valid_predictions.append({
                            'model': model_name,
                            'cat_name': result.get('cat_name'),
                            'cat_id': result.get('cat_id'),
                            'confidence': result.get('confidence', 0),
                            'weight': self.model_weights.get(model_name, 1.0),
                            'similarity_score': result.get('similarity_score', 0)
                        })
                        
                except Exception as e:
                    logger.error(f"{model_name}模型识别失败: {e}")
                    model_predictions[model_name] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            # 高级集成决策
            ensemble_result = self._advanced_ensemble_decision(valid_predictions, model_predictions)
            
            # 更新统计
            self._update_stats(ensemble_result, valid_predictions)
            
            return ensemble_result
            
        except Exception as e:
            logger.error(f"集成识别失败: {e}")
            return {
                'status': 'error',
                'cat_name': 'Unknown',
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _advanced_ensemble_decision(self, valid_predictions: List[Dict], 
                                  model_predictions: Dict) -> Dict:
        """高级集成决策算法"""
        if not valid_predictions:
            return {
                'status': 'unknown',
                'cat_name': 'Unknown',
                'confidence': 0.0,
                'ensemble_method': 'no_valid_predictions',
                'model_predictions': model_predictions
            }
        
        # 策略1: 加权投票 + 置信度提升
        cat_scores = {}
        
        for pred in valid_predictions:
            cat_id = pred['cat_id']
            if cat_id not in cat_scores:
                cat_scores[cat_id] = {
                    'cat_name': pred['cat_name'],
                    'weighted_score': 0.0,
                    'total_weight': 0.0,
                    'model_count': 0,
                    'max_confidence': 0.0,
                    'avg_similarity': 0.0
                }
            
            # 加权得分计算
            weight = pred['weight']
            confidence = pred['confidence']
            similarity = pred.get('similarity_score', confidence)
            
            # 综合得分 = (置信度 * 权重) + (相似度 * 权重 * 0.5)
            combined_score = (confidence * weight) + (similarity * weight * 0.5)
            
            cat_scores[cat_id]['weighted_score'] += combined_score
            cat_scores[cat_id]['total_weight'] += weight
            cat_scores[cat_id]['model_count'] += 1
            cat_scores[cat_id]['max_confidence'] = max(cat_scores[cat_id]['max_confidence'], confidence)
            cat_scores[cat_id]['avg_similarity'] += similarity
        
        # 计算最终得分
        for cat_id in cat_scores:
            cat_scores[cat_id]['avg_similarity'] /= cat_scores[cat_id]['model_count']
        
        if not cat_scores:
            return {
                'status': 'unknown',
                'cat_name': 'Unknown',
                'confidence': 0.0,
                'ensemble_method': 'advanced_weighted',
                'model_predictions': model_predictions
            }
        
        # 选择最佳候选
        best_cat_id = max(cat_scores.keys(), 
                         key=lambda x: cat_scores[x]['weighted_score'])
        best_score = cat_scores[best_cat_id]
        
        # 计算最终置信度
        base_confidence = best_score['weighted_score'] / best_score['total_weight']
        
        # 集成提升因子
        model_agreement_bonus = 0.0
        if best_score['model_count'] > 1:
            model_agreement_bonus = self.adaptive_thresholds['ensemble_boost']
            self.stats['model_agreements'] += 1
        
        # 高置信度提升
        high_confidence_bonus = 0.0
        if best_score['max_confidence'] > 0.9:
            high_confidence_bonus = 0.05
        
        final_confidence = min(1.0, base_confidence + model_agreement_bonus + high_confidence_bonus)
        
        # 自适应阈值判断
        effective_threshold = self._get_adaptive_threshold(best_score['model_count'])
        status = 'known' if final_confidence >= effective_threshold else 'uncertain'
        
        # 如果不确定，尝试降低阈值
        if status == 'uncertain' and final_confidence >= (effective_threshold - 0.1):
            status = 'known'
            self.stats['threshold_adjustments'] += 1
        
        return {
            'status': status,
            'cat_name': best_score['cat_name'] if status == 'known' else 'Unknown',
            'cat_id': best_cat_id if status == 'known' else None,
            'confidence': final_confidence,
            'ensemble_method': 'advanced_weighted',
            'model_agreement': best_score['model_count'] / len(self.models),
            'base_confidence': base_confidence,
            'ensemble_boost': model_agreement_bonus + high_confidence_bonus,
            'effective_threshold': effective_threshold,
            'alternatives': self._get_alternatives(cat_scores, best_cat_id),
            'model_predictions': model_predictions
        }
    
    def _get_adaptive_threshold(self, model_count: int) -> float:
        """获取自适应阈值"""
        base_threshold = self.adaptive_thresholds['medium_confidence']
        
        # 多模型一致时降低阈值
        if model_count > 1:
            return base_threshold - 0.1
        else:
            return base_threshold
    
    def _get_alternatives(self, cat_scores: Dict, best_cat_id: str) -> List[Dict]:
        """获取候选结果"""
        alternatives = []
        
        for cat_id, score_info in cat_scores.items():
            if cat_id != best_cat_id:
                alternatives.append({
                    'cat_name': score_info['cat_name'],
                    'cat_id': cat_id,
                    'confidence': score_info['weighted_score'] / score_info['total_weight']
                })
        
        alternatives.sort(key=lambda x: x['confidence'], reverse=True)
        return alternatives[:3]
    
    def _update_stats(self, result: Dict, predictions: List[Dict]):
        """更新统计信息"""
        self.stats['total_predictions'] += 1
        
        if result.get('ensemble_boost', 0) > 0:
            self.stats['ensemble_improvements'] += 1
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        total = self.stats['total_predictions']
        
        return {
            'ensemble_type': 'advanced_multi_model',
            'model_count': len(self.models),
            'total_predictions': total,
            'ensemble_improvement_rate': (self.stats['ensemble_improvements'] / total) if total > 0 else 0.0,
            'model_agreement_rate': (self.stats['model_agreements'] / total) if total > 0 else 0.0,
            'threshold_adjustment_rate': (self.stats['threshold_adjustments'] / total) if total > 0 else 0.0,
            'model_weights': self.model_weights,
            'adaptive_thresholds': self.adaptive_thresholds
        }
    
    def cleanup(self):
        """清理资源"""
        for model_name, model in self.models.items():
            try:
                model.cleanup()
                print(f"   ✅ {model_name}模型已清理")
            except:
                pass
        
        print("✅ 高级集成系统已清理")

def create_advanced_ensemble_recognizer(config: Dict) -> AdvancedEnsembleRecognizer:
    """创建高级集成识别器"""
    return AdvancedEnsembleRecognizer(config)

if __name__ == "__main__":
    # 测试高级集成系统
    config = {}
    
    ensemble = create_advanced_ensemble_recognizer(config)
    print("高级集成识别器创建成功")
    print(f"系统统计: {ensemble.get_system_stats()}")
    
    ensemble.cleanup()
