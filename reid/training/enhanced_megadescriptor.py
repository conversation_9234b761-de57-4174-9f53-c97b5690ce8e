#!/usr/bin/env python3
"""
增强版MegaDescriptor识别器 - 专门解决随机采样性能问题
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import timm
from torchvision import transforms
import cv2
import random

try:
    from .vit_large_model import create_megadescriptor_large_model
    from .feature_database import FeatureDatabase
    from .matching_engine import MatchingEngine
except ImportError:
    from vit_large_model import create_megadescriptor_large_model
    from feature_database import FeatureDatabase
    from matching_engine import MatchingEngine

logger = logging.getLogger(__name__)

class EnhancedMegaDescriptorRecognizer:
    """增强版MegaDescriptor识别器 - 提升随机采样性能"""
    
    def __init__(self, config: Dict):
        """初始化增强识别器"""
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型配置
        self.feature_dim = config.get('feature_dim', 1536)
        self.max_cats = config.get('max_cats', 100)
        self.confidence_threshold = config.get('confidence_threshold', 0.55)  # 降低阈值
        
        # 增强配置
        self.enable_tta = config.get('enable_tta', True)  # 测试时增强
        self.enable_feature_ensemble = config.get('enable_feature_ensemble', True)
        self.enable_adaptive_threshold = config.get('enable_adaptive_threshold', True)
        
        # 初始化组件
        self.model = None
        self.feature_db = None
        self.matching_engine = None
        self.transforms = None
        
        # 性能统计
        self.stats = {
            'total_predictions': 0,
            'correct_predictions': 0,
            'high_confidence_predictions': 0,
            'unknown_predictions': 0,
            'tta_improvements': 0
        }
        
        self._initialize_components()
        
    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化MegaDescriptor-L-384模型
            logger.info("正在初始化增强版MegaDescriptor-L-384模型...")
            self.model = create_megadescriptor_large_model(
                num_cats=self.max_cats,
                feature_dim=self.feature_dim,
                pretrained=True
            ).to(self.device)
            
            self.model.eval()
            logger.info("✅ 增强版MegaDescriptor-L-384模型初始化完成")
            
            # 初始化多种图像预处理策略
            self._initialize_transforms()
            
            # 初始化特征数据库
            db_path = self.config.get('feature_db_path', 'enhanced_megadescriptor_db')
            self.feature_db = FeatureDatabase(db_path=db_path)
            
            # 初始化匹配引擎 - 使用更宽松的阈值
            self.matching_engine = MatchingEngine(self.feature_db)
            
            # 优化匹配引擎阈值
            self.matching_engine.thresholds.update({
                'high_confidence': 0.75,  # 降低高置信度阈值
                'medium_confidence': 0.60,  # 降低中等置信度阈值
                'new_cat_threshold': 0.45,  # 降低新猫阈值
                'min_samples_for_registration': 3
            })
            
            logger.info("✅ 增强版识别器初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    def _initialize_transforms(self):
        """初始化多种变换策略"""
        # 基础变换
        self.base_transform = transforms.Compose([
            transforms.Resize((384, 384)),
            transforms.ToTensor(),
            transforms.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            )
        ])
        
        # TTA变换集合
        self.tta_transforms = [
            # 原始
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 水平翻转
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.RandomHorizontalFlip(p=1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 轻微旋转
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.RandomRotation(degrees=5),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 亮度调整
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.ColorJitter(brightness=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 多尺度
            transforms.Compose([
                transforms.Resize((416, 416)),
                transforms.CenterCrop((384, 384)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """注册新猫咪 - 使用增强特征提取"""
        try:
            logger.info(f"注册猫咪: {cat_name} (ID: {cat_id})")
            
            # 提取增强特征向量
            features = []
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    # 使用集成特征提取
                    feature = self._extract_enhanced_feature(img_path)
                    if feature is not None:
                        features.append(feature)
                        valid_images += 1
                        logger.debug(f"成功提取增强特征: {Path(img_path).name}")
                except Exception as e:
                    logger.warning(f"处理图像 {img_path} 失败: {e}")
                    continue
            
            if len(features) < 3:
                return {
                    'success': False,
                    'error': f'有效图像数量不足: {len(features)}/3'
                }
            
            # 存储到特征数据库
            registered_cat_id = self.feature_db.add_cat(cat_name=cat_name, cat_id=cat_id)
            
            # 添加特征向量
            success_count = 0
            for i, feature in enumerate(features):
                success = self.feature_db.add_feature(
                    cat_id=registered_cat_id,
                    feature_vector=feature,
                    image_path=image_paths[i] if i < len(image_paths) else f"image_{i}",
                    confidence=1.0
                )
                if success:
                    success_count += 1
            
            if success_count > 0:
                logger.info(f"✅ 成功注册猫咪: {cat_name} ({success_count}个增强特征)")
                return {
                    'success': True,
                    'cat_id': registered_cat_id,
                    'cat_name': cat_name,
                    'features_count': success_count
                }
            else:
                return {
                    'success': False,
                    'error': '特征数据库存储失败'
                }
                
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str, return_alternatives: bool = False,
                     confidence_threshold: Optional[float] = None) -> Dict:
        """识别猫咪 - 使用增强识别策略"""
        try:
            # 提取增强特征
            feature = self._extract_enhanced_feature(image_path)
            if feature is None:
                return {
                    'status': 'error',
                    'cat_name': 'Unknown',
                    'confidence': 0.0,
                    'error': '特征提取失败'
                }
            
            # 使用自适应阈值
            effective_threshold = confidence_threshold or self._calculate_adaptive_threshold()
            
            # 匹配
            match_result = self.matching_engine.match(
                query_feature=feature,
                return_alternatives=return_alternatives
            )
            
            # 如果使用TTA且初始结果不理想，尝试TTA增强
            if (self.enable_tta and 
                match_result.confidence < 0.8 and 
                match_result.status != 'known'):
                
                tta_result = self._tta_recognition(image_path, return_alternatives)
                if tta_result and tta_result.confidence > match_result.confidence:
                    match_result = tta_result
                    self.stats['tta_improvements'] += 1
            
            # 更新统计
            self.stats['total_predictions'] += 1
            
            if match_result.status == 'known':
                if match_result.confidence > 0.9:
                    self.stats['high_confidence_predictions'] += 1
            else:
                self.stats['unknown_predictions'] += 1
            
            # 转换为字典格式
            result_dict = {
                'status': match_result.status,
                'cat_name': match_result.cat_name or 'Unknown',
                'cat_id': match_result.cat_id,
                'confidence': match_result.confidence,
                'similarity_score': match_result.similarity_score,
                'match_type': match_result.match_type,
                'alternatives': match_result.alternatives,
                'processing_time': match_result.processing_time,
                'model_type': 'Enhanced-MegaDescriptor-L-384',
                'feature_dim': self.feature_dim,
                'tta_used': hasattr(match_result, 'tta_used')
            }
            
            return result_dict
            
        except Exception as e:
            logger.error(f"识别失败: {e}")
            return {
                'status': 'error',
                'cat_name': 'Unknown',
                'confidence': 0.0,
                'error': str(e)
            }
    
    def _extract_enhanced_feature(self, image_path: str) -> Optional[np.ndarray]:
        """提取增强特征向量"""
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            if self.enable_feature_ensemble:
                # 集成多个变换的特征
                features = []
                
                for transform in self.tta_transforms[:3]:  # 使用前3个变换
                    input_tensor = transform(image).unsqueeze(0).to(self.device)
                    
                    with torch.no_grad():
                        feature = self.model.extract_features(input_tensor)
                        feature = feature.cpu().numpy().flatten()
                        features.append(feature)
                
                # 平均集成
                ensemble_feature = np.mean(features, axis=0)
                # L2归一化
                ensemble_feature = ensemble_feature / np.linalg.norm(ensemble_feature)
                
                return ensemble_feature
            else:
                # 单一特征提取
                input_tensor = self.base_transform(image).unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    features = self.model.extract_features(input_tensor)
                    features = features.cpu().numpy().flatten()
                    # L2归一化
                    features = features / np.linalg.norm(features)
                
                return features
                
        except Exception as e:
            logger.error(f"增强特征提取失败 {image_path}: {e}")
            return None
    
    def _tta_recognition(self, image_path: str, return_alternatives: bool) -> Optional:
        """测试时增强识别"""
        try:
            image = Image.open(image_path).convert('RGB')
            
            all_results = []
            
            # 对每个TTA变换进行识别
            for transform in self.tta_transforms:
                input_tensor = transform(image).unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    feature = self.model.extract_features(input_tensor)
                    feature = feature.cpu().numpy().flatten()
                    feature = feature / np.linalg.norm(feature)
                
                # 匹配
                result = self.matching_engine.match(
                    query_feature=feature,
                    return_alternatives=return_alternatives
                )
                
                if result.status == 'known':
                    all_results.append(result)
            
            if all_results:
                # 选择置信度最高的结果
                best_result = max(all_results, key=lambda x: x.confidence)
                best_result.tta_used = True
                return best_result
            
            return None
            
        except Exception as e:
            logger.error(f"TTA识别失败: {e}")
            return None
    
    def _calculate_adaptive_threshold(self) -> float:
        """计算自适应阈值"""
        if not self.enable_adaptive_threshold:
            return self.confidence_threshold
        
        # 基于已注册猫咪数量调整阈值
        num_cats = self.feature_db.get_cat_count()
        
        if num_cats <= 5:
            return 0.60
        elif num_cats <= 10:
            return 0.55
        else:
            return 0.50
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        total = self.stats['total_predictions']
        
        return {
            'registered_cats': self.feature_db.get_cat_count() if self.feature_db else 0,
            'total_predictions': total,
            'correct_predictions': self.stats['correct_predictions'],
            'high_confidence_rate': (self.stats['high_confidence_predictions'] / total) if total > 0 else 0.0,
            'unknown_rate': (self.stats['unknown_predictions'] / total) if total > 0 else 0.0,
            'tta_improvement_rate': (self.stats['tta_improvements'] / total) if total > 0 else 0.0,
            'model_type': 'Enhanced-MegaDescriptor-L-384',
            'feature_dim': self.feature_dim,
            'input_size': '384x384',
            'enhancements': {
                'tta_enabled': self.enable_tta,
                'feature_ensemble': self.enable_feature_ensemble,
                'adaptive_threshold': self.enable_adaptive_threshold
            }
        }
    
    def cleanup(self):
        """清理资源"""
        if self.feature_db:
            try:
                self.feature_db._save_metadata()
            except:
                pass
        logger.info("增强版MegaDescriptor识别器已清理")

def create_enhanced_megadescriptor_recognizer(config: Dict) -> EnhancedMegaDescriptorRecognizer:
    """创建增强版MegaDescriptor识别器"""
    return EnhancedMegaDescriptorRecognizer(config)

if __name__ == "__main__":
    # 测试增强版识别器
    config = {
        'feature_dim': 1536,
        'max_cats': 50,
        'confidence_threshold': 0.55,
        'feature_db_path': 'test_enhanced_db',
        'enable_tta': True,
        'enable_feature_ensemble': True,
        'enable_adaptive_threshold': True
    }
    
    recognizer = create_enhanced_megadescriptor_recognizer(config)
    print("增强版MegaDescriptor识别器创建成功")
    print(f"系统统计: {recognizer.get_system_stats()}")
    
    recognizer.cleanup()
