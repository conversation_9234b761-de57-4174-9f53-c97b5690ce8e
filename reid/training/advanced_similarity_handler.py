#!/usr/bin/env python3
"""
高级相似性处理器 - 专门解决相似猫咪识别问题
"""

import numpy as np
import cv2
from pathlib import Path
from collections import defaultdict
import json
import time
from sklearn.cluster import DBSCAN
from sklearn.metrics.pairwise import cosine_similarity

class AdvancedSimilarityHandler:
    """高级相似性处理器"""
    
    def __init__(self, similarity_threshold=0.85):
        self.similarity_threshold = similarity_threshold
        self.similar_pairs = set()
        self.cat_features = {}
        self.similarity_matrix = {}
        
        # 预定义的已知相似猫咪对
        self.known_similar_pairs = [
            ('0322', '0431'), ('0378', '0421'), ('0015', '0202'),
            ('0001', '0010'), ('0004', '0007'), ('0379', '0323')
        ]
        
        print("🔍 初始化高级相似性处理器")
    
    def extract_visual_features(self, image_path):
        """提取视觉特征用于相似性分析"""
        try:
            img = cv2.imread(image_path)
            if img is None:
                return None
            
            # 转换为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 提取多种特征
            features = {}
            
            # 1. 纹理特征 (LBP)
            features['texture'] = self._extract_lbp_features(gray)
            
            # 2. 颜色特征
            features['color'] = self._extract_color_features(img)
            
            # 3. 边缘特征
            features['edges'] = self._extract_edge_features(gray)
            
            # 4. 形状特征
            features['shape'] = self._extract_shape_features(gray)
            
            return features
            
        except Exception as e:
            print(f"特征提取失败 {image_path}: {e}")
            return None
    
    def _extract_lbp_features(self, gray):
        """提取LBP纹理特征"""
        # 简化的LBP实现
        lbp = np.zeros_like(gray)
        for i in range(1, gray.shape[0]-1):
            for j in range(1, gray.shape[1]-1):
                center = gray[i, j]
                code = 0
                code |= (gray[i-1, j-1] >= center) << 7
                code |= (gray[i-1, j] >= center) << 6
                code |= (gray[i-1, j+1] >= center) << 5
                code |= (gray[i, j+1] >= center) << 4
                code |= (gray[i+1, j+1] >= center) << 3
                code |= (gray[i+1, j] >= center) << 2
                code |= (gray[i+1, j-1] >= center) << 1
                code |= (gray[i, j-1] >= center) << 0
                lbp[i, j] = code
        
        # 计算LBP直方图
        hist, _ = np.histogram(lbp.ravel(), bins=256, range=(0, 256))
        return hist / np.sum(hist)  # 归一化
    
    def _extract_color_features(self, img):
        """提取颜色特征"""
        # 转换到HSV色彩空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # 计算各通道直方图
        h_hist = cv2.calcHist([hsv], [0], None, [180], [0, 180])
        s_hist = cv2.calcHist([hsv], [1], None, [256], [0, 256])
        v_hist = cv2.calcHist([hsv], [2], None, [256], [0, 256])
        
        # 归一化并连接
        h_hist = h_hist.flatten() / np.sum(h_hist)
        s_hist = s_hist.flatten() / np.sum(s_hist)
        v_hist = v_hist.flatten() / np.sum(v_hist)
        
        return np.concatenate([h_hist, s_hist[:64], v_hist[:64]])  # 降维
    
    def _extract_edge_features(self, gray):
        """提取边缘特征"""
        # Canny边缘检测
        edges = cv2.Canny(gray, 50, 150)
        
        # 计算边缘密度和方向
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        # 计算边缘方向直方图
        sobelx = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        sobely = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        angles = np.arctan2(sobely, sobelx)
        
        hist, _ = np.histogram(angles.ravel(), bins=36, range=(-np.pi, np.pi))
        hist = hist / np.sum(hist)
        
        return np.concatenate([[edge_density], hist])
    
    def _extract_shape_features(self, gray):
        """提取形状特征"""
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return np.zeros(7)
        
        # 找到最大轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 计算形状特征
        area = cv2.contourArea(largest_contour)
        perimeter = cv2.arcLength(largest_contour, True)
        
        if perimeter == 0:
            return np.zeros(7)
        
        # 圆形度
        circularity = 4 * np.pi * area / (perimeter * perimeter)
        
        # 长宽比
        x, y, w, h = cv2.boundingRect(largest_contour)
        aspect_ratio = float(w) / h if h != 0 else 0
        
        # 凸包比
        hull = cv2.convexHull(largest_contour)
        hull_area = cv2.contourArea(hull)
        solidity = area / hull_area if hull_area != 0 else 0
        
        # 矩特征
        moments = cv2.moments(largest_contour)
        if moments['m00'] != 0:
            hu_moments = cv2.HuMoments(moments).flatten()[:4]
        else:
            hu_moments = np.zeros(4)
        
        return np.concatenate([[circularity, aspect_ratio, solidity], hu_moments])
    
    def analyze_cat_similarity(self, cat_id, image_paths):
        """分析猫咪与其他猫咪的相似性"""
        print(f"   分析猫咪 {cat_id} 的相似性")
        
        # 提取该猫咪的特征
        cat_features = []
        for img_path in image_paths[:5]:  # 使用前5张图片
            features = self.extract_visual_features(img_path)
            if features is not None:
                # 合并所有特征
                combined = np.concatenate([
                    features['texture'],
                    features['color'], 
                    features['edges'],
                    features['shape']
                ])
                cat_features.append(combined)
        
        if not cat_features:
            return 'medium'
        
        # 计算平均特征
        avg_features = np.mean(cat_features, axis=0)
        self.cat_features[cat_id] = avg_features
        
        # 检查是否为已知相似猫咪
        for pair in self.known_similar_pairs:
            if cat_id in pair:
                print(f"     检测到已知相似猫咪: {pair}")
                return 'hard'
        
        # 与已有猫咪比较相似性
        max_similarity = 0
        for other_cat_id, other_features in self.cat_features.items():
            if other_cat_id != cat_id:
                similarity = cosine_similarity([avg_features], [other_features])[0][0]
                max_similarity = max(max_similarity, similarity)
                
                if similarity > self.similarity_threshold:
                    self.similar_pairs.add((min(cat_id, other_cat_id), max(cat_id, other_cat_id)))
                    print(f"     发现相似猫咪对: {cat_id} ↔ {other_cat_id} (相似度: {similarity:.3f})")
        
        # 根据最大相似度确定难度
        if max_similarity > 0.90:
            return 'hard'
        elif max_similarity > 0.80:
            return 'medium'
        else:
            return 'easy'
    
    def get_similarity_penalty(self, cat_id, confidence):
        """获取相似性惩罚"""
        # 检查是否为相似猫咪
        is_similar = False
        for pair in self.similar_pairs:
            if cat_id in pair:
                is_similar = True
                break
        
        if not is_similar:
            for pair in self.known_similar_pairs:
                if cat_id in pair:
                    is_similar = True
                    break
        
        if is_similar:
            if confidence < 0.6:
                # 低置信度，可能是混淆，应用惩罚
                penalty = 0.15
                print(f"     相似猫咪惩罚: -{penalty:.3f}")
                return -penalty
            elif confidence > 0.8:
                # 高置信度，给予奖励
                boost = 0.08
                print(f"     高置信度奖励: +{boost:.3f}")
                return boost
        
        return 0.0
    
    def get_contrastive_boost(self, cat_id, predicted_cat_id, confidence):
        """获取对比增强提升"""
        # 检查预测是否涉及相似猫咪对
        for pair in list(self.similar_pairs) + self.known_similar_pairs:
            if cat_id in pair and predicted_cat_id in pair and cat_id != predicted_cat_id:
                # 这是相似猫咪间的混淆，需要更高的置信度
                if confidence > 0.7:
                    boost = 0.12
                    print(f"     对比增强提升: +{boost:.3f}")
                    return boost
                else:
                    penalty = 0.20
                    print(f"     相似混淆惩罚: -{penalty:.3f}")
                    return -penalty
        
        return 0.0
    
    def get_similarity_stats(self):
        """获取相似性统计"""
        return {
            'total_cats': len(self.cat_features),
            'similar_pairs': len(self.similar_pairs),
            'known_similar_pairs': len(self.known_similar_pairs),
            'similarity_threshold': self.similarity_threshold
        }

def create_advanced_similarity_handler(similarity_threshold=0.85):
    """创建高级相似性处理器"""
    return AdvancedSimilarityHandler(similarity_threshold)

if __name__ == "__main__":
    # 测试相似性处理器
    print("🧪 测试高级相似性处理器")
    
    handler = create_advanced_similarity_handler()
    
    # 模拟测试
    test_cats = ['0322', '0431', '0100']
    
    for cat_id in test_cats:
        # 模拟图片路径
        image_paths = [f"test_{cat_id}_{i}.jpg" for i in range(5)]
        
        # 分析相似性（这里只是模拟，实际需要真实图片）
        print(f"分析猫咪 {cat_id} 的相似性")
    
    stats = handler.get_similarity_stats()
    print(f"\n📊 相似性统计: {stats}")
    
    print("✅ 高级相似性处理器测试完成")
