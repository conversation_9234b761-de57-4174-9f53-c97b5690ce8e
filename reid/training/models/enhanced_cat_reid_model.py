#!/usr/bin/env python3
"""
增强版猫个体识别模型
基于MegaDescriptor进行中期改进，支持更大规模的个体识别
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
import math
from typing import Optional, Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class MultiHeadAttention(nn.Module):
    """多头注意力机制，用于增强特征表示"""
    
    def __init__(self, feature_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_heads = num_heads
        self.head_dim = feature_dim // num_heads
        
        assert feature_dim % num_heads == 0, "feature_dim must be divisible by num_heads"
        
        self.query = nn.Linear(feature_dim, feature_dim)
        self.key = nn.Linear(feature_dim, feature_dim)
        self.value = nn.Linear(feature_dim, feature_dim)
        self.dropout = nn.Dropout(dropout)
        self.output_proj = nn.Linear(feature_dim, feature_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        batch_size = x.size(0)
        
        # 计算Q, K, V
        Q = self.query(x).view(batch_size, self.num_heads, self.head_dim)
        K = self.key(x).view(batch_size, self.num_heads, self.head_dim)
        V = self.value(x).view(batch_size, self.num_heads, self.head_dim)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力
        attended = torch.matmul(attention_weights, V)
        attended = attended.view(batch_size, self.feature_dim)
        
        # 输出投影
        output = self.output_proj(attended)
        return output + x  # 残差连接

class CenterLoss(nn.Module):
    """Center Loss - 增强类内紧密性"""
    
    def __init__(self, num_classes: int, feature_dim: int, alpha: float = 0.5):
        super().__init__()
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        self.alpha = alpha
        
        # 可学习的类中心
        self.centers = nn.Parameter(torch.randn(num_classes, feature_dim))
        nn.init.kaiming_normal_(self.centers)
        
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        计算Center Loss
        
        Args:
            features: 特征向量 (B, D)
            labels: 标签 (B,)
            
        Returns:
            center_loss: Center Loss值
        """
        batch_size = features.size(0)
        
        # 获取对应的类中心
        centers_batch = self.centers[labels]  # (B, D)
        
        # 计算特征与类中心的距离
        center_loss = F.mse_loss(features, centers_batch)
        
        return center_loss

class ArcFaceLoss(nn.Module):
    """ArcFace Loss - 增强类间分离性"""
    
    def __init__(self, feature_dim: int, num_classes: int, margin: float = 0.5, scale: float = 64.0):
        super().__init__()
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        self.margin = margin
        self.scale = scale
        
        # 权重矩阵
        self.weight = nn.Parameter(torch.randn(num_classes, feature_dim))
        nn.init.kaiming_normal_(self.weight)
        
    def forward(self, features: torch.Tensor, labels: torch.Tensor) -> torch.Tensor:
        """
        计算ArcFace Loss
        
        Args:
            features: L2归一化的特征向量 (B, D)
            labels: 标签 (B,)
            
        Returns:
            arcface_loss: ArcFace Loss值
        """
        # 归一化权重
        normalized_weight = F.normalize(self.weight, p=2, dim=1)
        
        # 计算余弦相似度
        cosine = F.linear(features, normalized_weight)  # (B, num_classes)
        
        # 计算角度
        theta = torch.acos(torch.clamp(cosine, -1.0 + 1e-7, 1.0 - 1e-7))
        
        # 为目标类添加margin
        one_hot = F.one_hot(labels, self.num_classes).float()
        target_theta = theta + self.margin * one_hot
        
        # 转换回余弦值
        target_cosine = torch.cos(target_theta)
        
        # 应用缩放
        logits = target_cosine * self.scale
        
        # 计算交叉熵损失
        return F.cross_entropy(logits, labels)

class EnhancedCatReidModel(nn.Module):
    """增强版猫个体识别模型"""
    
    def __init__(
        self,
        num_classes: int = 3,
        backbone: str = "hf-hub:BVRA/MegaDescriptor-T-224",
        feature_dim: int = 1024,  # 增加特征维度
        dropout: float = 0.1,
        pretrained: bool = True,
        freeze_backbone: bool = False,
        use_attention: bool = True,
        attention_heads: int = 8,
        use_arcface: bool = False,
        arcface_margin: float = 0.5,
        arcface_scale: float = 64.0
    ):
        """
        增强版模型初始化
        
        Args:
            num_classes: 猫个体数量
            backbone: 骨干网络名称
            feature_dim: 特征维度（增加到1024）
            dropout: Dropout比例
            pretrained: 是否使用预训练权重
            freeze_backbone: 是否冻结骨干网络
            use_attention: 是否使用注意力机制
            attention_heads: 注意力头数
            use_arcface: 是否使用ArcFace损失
            arcface_margin: ArcFace margin
            arcface_scale: ArcFace scale
        """
        super().__init__()
        
        self.num_classes = num_classes
        self.feature_dim = feature_dim
        self.use_attention = use_attention
        self.use_arcface = use_arcface
        
        # 加载预训练的MegaDescriptor
        self.backbone = timm.create_model(
            backbone, 
            pretrained=pretrained,
            num_classes=0  # 移除分类头
        )
        
        # 获取backbone输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 增强的特征投影层
        self.feature_projector = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 注意力机制
        if use_attention:
            self.attention = MultiHeadAttention(feature_dim, attention_heads, dropout)
        
        # 分类器头部
        if use_arcface:
            self.classifier = ArcFaceLoss(feature_dim, num_classes, arcface_margin, arcface_scale)
        else:
            self.classifier = nn.Linear(feature_dim, num_classes)
            # 温度参数
            self.temperature = nn.Parameter(torch.ones(1) * 10.0)
        
        # Center Loss
        self.center_loss = CenterLoss(num_classes, feature_dim)
        
        # 是否冻结骨干网络
        if freeze_backbone:
            self._freeze_backbone()
        
        self._init_weights()
        
        logger.info(f"EnhancedCatReidModel初始化完成:")
        logger.info(f"  - 骨干网络: {backbone}")
        logger.info(f"  - 猫个体数: {num_classes}")
        logger.info(f"  - 特征维度: {feature_dim}")
        logger.info(f"  - 骨干网络输出维度: {backbone_dim}")
        logger.info(f"  - 使用注意力: {use_attention}")
        logger.info(f"  - 使用ArcFace: {use_arcface}")
        logger.info(f"  - 冻结骨干网络: {freeze_backbone}")
    
    def _freeze_backbone(self):
        """冻结骨干网络参数"""
        for param in self.backbone.parameters():
            param.requires_grad = False
        logger.info("骨干网络参数已冻结")
    
    def _init_weights(self):
        """初始化权重"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x: torch.Tensor, labels: Optional[torch.Tensor] = None, 
                return_features: bool = False):
        """
        前向传播
        
        Args:
            x: 输入图像 (B, 3, H, W)
            labels: 标签，训练时需要（用于ArcFace）
            return_features: 是否返回特征向量
            
        Returns:
            根据参数返回不同的输出
        """
        # 骨干网络特征提取
        backbone_features = self.backbone(x)  # (B, backbone_dim)
        
        # 特征投影
        features = self.feature_projector(backbone_features)  # (B, feature_dim)
        
        # 注意力增强
        if self.use_attention:
            features = self.attention(features)
        
        # L2归一化特征
        features = F.normalize(features, p=2, dim=1)
        
        # 分类
        if self.use_arcface and labels is not None:
            # ArcFace需要标签
            logits = self.classifier(features, labels)
        else:
            # 普通分类器
            if hasattr(self, 'temperature'):
                logits = self.classifier(features) / self.temperature
            else:
                logits = self.classifier(features)
        
        if return_features:
            return features, logits
        else:
            return logits
    
    def extract_features(self, x: torch.Tensor) -> torch.Tensor:
        """提取特征向量（用于相似度计算）"""
        with torch.no_grad():
            backbone_features = self.backbone(x)
            features = self.feature_projector(backbone_features)
            
            if self.use_attention:
                features = self.attention(features)
            
            # L2归一化
            features = F.normalize(features, p=2, dim=1)
        return features

class EnhancedCombinedLoss(nn.Module):
    """增强版组合损失函数"""
    
    def __init__(
        self, 
        classification_weight: float = 1.0, 
        triplet_weight: float = 1.0,
        center_weight: float = 0.5,
        triplet_margin: float = 0.3,
        triplet_mining: str = 'batch_hard',
        label_smoothing: float = 0.1
    ):
        super().__init__()
        self.classification_weight = classification_weight
        self.triplet_weight = triplet_weight
        self.center_weight = center_weight
        
        # 导入原有的TripletLoss
        from .cat_reid_model import TripletLoss
        
        # 使用标签平滑的交叉熵损失
        self.classification_loss = nn.CrossEntropyLoss(label_smoothing=label_smoothing)
        self.triplet_loss = TripletLoss(margin=triplet_margin, mining=triplet_mining)
        
    def forward(self, model: EnhancedCatReidModel, features: torch.Tensor, 
                logits: torch.Tensor, labels: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        计算增强版组合损失
        
        Args:
            model: 模型实例（用于获取center_loss）
            features: 特征向量 (B, D)
            logits: 分类输出 (B, C)
            labels: 真实标签 (B,)
            
        Returns:
            loss_dict: 包含各种损失的字典
        """
        # 分类损失（如果使用ArcFace，logits已经包含了ArcFace损失）
        if model.use_arcface:
            cls_loss = logits  # ArcFace返回的就是损失值
        else:
            cls_loss = self.classification_loss(logits, labels)
        
        # Triplet损失
        triplet_loss = self.triplet_loss(features, labels)
        
        # Center损失
        center_loss = model.center_loss(features, labels)
        
        # 总损失
        total_loss = (self.classification_weight * cls_loss + 
                     self.triplet_weight * triplet_loss +
                     self.center_weight * center_loss)
        
        return {
            'total_loss': total_loss,
            'classification_loss': cls_loss,
            'triplet_loss': triplet_loss,
            'center_loss': center_loss
        }

def create_enhanced_model(num_classes: int, **kwargs) -> EnhancedCatReidModel:
    """创建增强版模型的工厂函数"""
    return EnhancedCatReidModel(num_classes=num_classes, **kwargs)

# 为长远规划提供的接口
class ModelRegistry:
    """模型注册表 - 为未来扩展提供接口"""
    
    _models = {
        'basic': 'cat_reid_model.CatReidModel',
        'enhanced': 'enhanced_cat_reid_model.EnhancedCatReidModel',
        # 为未来模型预留位置
        'hierarchical': None,  # 分层识别模型
        'multimodal': None,    # 多模态融合模型
        'online_learning': None, # 在线学习模型
    }
    
    @classmethod
    def get_model(cls, model_name: str, **kwargs):
        """获取指定的模型"""
        if model_name not in cls._models:
            raise ValueError(f"Unknown model: {model_name}")
        
        if model_name == 'basic':
            from .cat_reid_model import CatReidModel
            return CatReidModel(**kwargs)
        elif model_name == 'enhanced':
            return EnhancedCatReidModel(**kwargs)
        else:
            raise NotImplementedError(f"Model {model_name} not implemented yet")
    
    @classmethod
    def register_model(cls, name: str, model_class: str):
        """注册新模型"""
        cls._models[name] = model_class

if __name__ == "__main__":
    # 测试增强版模型
    model = EnhancedCatReidModel(num_classes=50, feature_dim=1024, use_attention=True)
    
    # 测试前向传播
    x = torch.randn(4, 3, 224, 224)
    labels = torch.randint(0, 50, (4,))
    
    features, logits = model(x, labels, return_features=True)
    
    print(f"输入形状: {x.shape}")
    print(f"特征形状: {features.shape}")
    print(f"分类输出形状: {logits.shape}")
    
    # 测试损失函数
    loss_fn = EnhancedCombinedLoss()
    loss_dict = loss_fn(model, features, logits, labels)
    
    print(f"损失字典: {loss_dict}")
    
    print("增强版模型测试完成！")
