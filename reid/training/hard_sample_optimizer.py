#!/usr/bin/env python3
"""
困难样本优化器 - 专门处理识别失败的猫咪
"""

import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import logging
from pathlib import Path
from typing import List, Dict, Tuple
import json
import random
import cv2
from torchvision import transforms

# 添加训练模块路径
import sys
sys.path.append(str(Path(__file__).parent))

try:
    from enhanced_megadescriptor import create_enhanced_megadescriptor_recognizer
    from feature_database import FeatureDatabase
except ImportError:
    pass

logger = logging.getLogger(__name__)

class HardSampleOptimizer:
    """困难样本优化器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.dataset_path = Path(config['dataset_path'])
        
        # 从失败分析中获取的问题猫咪
        self.problem_cats = ['0322', '0431', '0378', '0421', '0015']
        
        # 图像增强策略
        self.augmentation_transforms = [
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.RandomHorizontalFlip(p=1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.RandomRotation(degrees=15),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            transforms.Compose([
                transforms.Resize((384, 384)),
                transforms.ColorJitter(brightness=0.2, contrast=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            transforms.Compose([
                transforms.Resize((416, 416)),
                transforms.CenterCrop((384, 384)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
        
        logger.info("困难样本优化器初始化完成")
    
    def analyze_problem_cats(self):
        """分析问题猫咪的特征"""
        print("🔍 分析问题猫咪特征")
        print("-" * 40)
        
        problem_analysis = {}
        
        for cat_id in self.problem_cats:
            cat_dir = self.dataset_path / cat_id
            if not cat_dir.exists():
                continue
            
            images = list(cat_dir.glob('*.jpg')) + list(cat_dir.glob('*.JPG'))
            
            # 分析图片质量
            quality_scores = []
            brightness_scores = []
            
            for img_path in images[:10]:  # 分析前10张
                try:
                    img = cv2.imread(str(img_path))
                    if img is None:
                        continue
                    
                    # 计算图片质量指标
                    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
                    
                    # 清晰度 (Laplacian方差)
                    clarity = cv2.Laplacian(gray, cv2.CV_64F).var()
                    quality_scores.append(clarity)
                    
                    # 亮度
                    brightness = np.mean(gray)
                    brightness_scores.append(brightness)
                    
                except Exception as e:
                    continue
            
            if quality_scores:
                problem_analysis[cat_id] = {
                    'avg_clarity': np.mean(quality_scores),
                    'avg_brightness': np.mean(brightness_scores),
                    'image_count': len(images),
                    'quality_std': np.std(quality_scores)
                }
                
                print(f"   猫咪 {cat_id}:")
                print(f"     图片数量: {len(images)}")
                print(f"     平均清晰度: {np.mean(quality_scores):.1f}")
                print(f"     平均亮度: {np.mean(brightness_scores):.1f}")
                print(f"     质量标准差: {np.std(quality_scores):.1f}")
        
        return problem_analysis
    
    def generate_enhanced_features(self, cat_id: str, num_augmentations: int = 20):
        """为问题猫咪生成增强特征"""
        print(f"🔧 为猫咪 {cat_id} 生成增强特征")
        
        cat_dir = self.dataset_path / cat_id
        if not cat_dir.exists():
            return []
        
        images = list(cat_dir.glob('*.jpg')) + list(cat_dir.glob('*.JPG'))
        
        # 创建临时识别器来提取特征
        config = {
            'feature_dim': 1536,
            'max_cats': 50,
            'confidence_threshold': 0.50,
            'feature_db_path': f'temp_enhanced_db_{cat_id}',
            'enable_tta': True,
            'enable_feature_ensemble': True,
            'enable_adaptive_threshold': True
        }
        
        try:
            from enhanced_megadescriptor import create_enhanced_megadescriptor_recognizer
            recognizer = create_enhanced_megadescriptor_recognizer(config)
            
            enhanced_features = []
            
            # 对每张图片应用多种增强
            for img_path in images[:8]:  # 使用前8张图片
                try:
                    # 原始特征
                    original_feature = recognizer._extract_enhanced_feature(str(img_path))
                    if original_feature is not None:
                        enhanced_features.append({
                            'feature': original_feature,
                            'source': f"{img_path.name}_original",
                            'augmentation': 'none'
                        })
                    
                    # 增强特征
                    image = Image.open(img_path).convert('RGB')
                    
                    for i, transform in enumerate(self.augmentation_transforms):
                        try:
                            # 应用变换
                            augmented_tensor = transform(image)
                            
                            # 保存为临时文件
                            temp_path = f"/tmp/temp_aug_{cat_id}_{img_path.stem}_{i}.jpg"
                            augmented_pil = transforms.ToPILImage()(augmented_tensor)
                            augmented_pil.save(temp_path)
                            
                            # 提取特征
                            aug_feature = recognizer._extract_enhanced_feature(temp_path)
                            if aug_feature is not None:
                                enhanced_features.append({
                                    'feature': aug_feature,
                                    'source': f"{img_path.name}_aug_{i}",
                                    'augmentation': f'transform_{i}'
                                })
                            
                            # 清理临时文件
                            Path(temp_path).unlink(missing_ok=True)
                            
                        except Exception as e:
                            logger.warning(f"增强失败 {img_path} transform {i}: {e}")
                            continue
                
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            recognizer.cleanup()
            
            print(f"   生成了 {len(enhanced_features)} 个增强特征")
            return enhanced_features
            
        except Exception as e:
            logger.error(f"生成增强特征失败: {e}")
            return []
    
    def optimize_problem_cats(self):
        """优化问题猫咪的识别"""
        print("🚀 开始优化问题猫咪")
        print("=" * 50)
        
        # 分析问题猫咪
        analysis = self.analyze_problem_cats()
        
        # 为每只问题猫咪生成增强特征
        all_enhanced_features = {}
        
        for cat_id in self.problem_cats:
            if cat_id in analysis:
                enhanced_features = self.generate_enhanced_features(cat_id, num_augmentations=20)
                if enhanced_features:
                    all_enhanced_features[cat_id] = enhanced_features
        
        # 保存增强特征
        enhanced_data = {
            'timestamp': str(Path().cwd()),
            'problem_cats': self.problem_cats,
            'analysis': analysis,
            'enhanced_features': {}
        }
        
        # 转换numpy数组为列表以便JSON序列化
        for cat_id, features in all_enhanced_features.items():
            enhanced_data['enhanced_features'][cat_id] = []
            for feat in features:
                enhanced_data['enhanced_features'][cat_id].append({
                    'feature': feat['feature'].tolist(),
                    'source': feat['source'],
                    'augmentation': feat['augmentation']
                })
        
        # 保存到文件
        output_file = "hard_sample_optimization_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 优化结果已保存: {output_file}")
        
        return enhanced_data
    
    def create_optimized_recognizer(self):
        """创建优化后的识别器"""
        print("🔧 创建优化后的识别器")
        
        # 加载增强特征
        enhanced_file = "hard_sample_optimization_results.json"
        if not Path(enhanced_file).exists():
            print("❌ 未找到增强特征文件，请先运行optimize_problem_cats()")
            return None
        
        with open(enhanced_file, 'r', encoding='utf-8') as f:
            enhanced_data = json.load(f)
        
        # 创建优化识别器
        config = {
            'feature_dim': 1536,
            'max_cats': 50,
            'confidence_threshold': 0.45,  # 进一步降低阈值
            'feature_db_path': 'optimized_hard_sample_db',
            'enable_tta': True,
            'enable_feature_ensemble': True,
            'enable_adaptive_threshold': True
        }
        
        try:
            from enhanced_megadescriptor import create_enhanced_megadescriptor_recognizer
            recognizer = create_enhanced_megadescriptor_recognizer(config)
            
            # 注册所有猫咪（包括增强特征）
            for cat_id, features in enhanced_data['enhanced_features'].items():
                # 转换回numpy数组
                feature_vectors = [np.array(feat['feature']) for feat in features]
                image_paths = [feat['source'] for feat in features]
                
                # 注册猫咪
                result = recognizer.register_cat(
                    cat_id=cat_id,
                    cat_name=f"OptimizedCat_{cat_id}",
                    image_paths=image_paths[:8]  # 使用前8个特征
                )
                
                if result.get('success', False):
                    print(f"   ✅ 优化注册猫咪 {cat_id}: {result.get('features_count', 0)}个特征")
                else:
                    print(f"   ❌ 优化注册失败 {cat_id}: {result.get('error', 'Unknown')}")
            
            return recognizer
            
        except Exception as e:
            logger.error(f"创建优化识别器失败: {e}")
            return None

def optimize_hard_samples():
    """主优化函数"""
    config = {
        'dataset_path': '/home/<USER>/animsi/caby_training/dataset/cat_individual_images'
    }
    
    optimizer = HardSampleOptimizer(config)
    
    # 执行优化
    enhanced_data = optimizer.optimize_problem_cats()
    
    # 创建优化识别器
    optimized_recognizer = optimizer.create_optimized_recognizer()
    
    if optimized_recognizer:
        print("✅ 困难样本优化完成")
        return optimized_recognizer
    else:
        print("❌ 困难样本优化失败")
        return None

if __name__ == "__main__":
    print("🚀 开始困难样本优化")
    recognizer = optimize_hard_samples()
    
    if recognizer:
        print("✅ 优化完成，可以进行测试")
        recognizer.cleanup()
    else:
        print("❌ 优化失败")
