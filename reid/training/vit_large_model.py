#!/usr/bin/env python3
"""
MegaDescriptor-L-384 大参数模型实现 - 用于高精度猫咪识别
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class MegaDescriptorLargeCatModel(nn.Module):
    """基于MegaDescriptor-L-384的猫咪识别模型"""

    def __init__(self, num_cats=50, feature_dim=1536, dropout_rate=0.1, pretrained=True):
        """
        初始化MegaDescriptor-L-384模型

        Args:
            num_cats: 猫咪类别数
            feature_dim: 特征维度 (MegaDescriptor-L-384输出1536维)
            dropout_rate: Dropout率
            pretrained: 是否使用预训练权重
        """
        super(MegaDescriptorLargeCatModel, self).__init__()

        self.num_cats = num_cats
        self.feature_dim = feature_dim
        self.backbone_dim = 1536  # MegaDescriptor-L-384的输出维度

        # 使用BVRA/MegaDescriptor-L-384作为骨干网络
        try:
            self.backbone = timm.create_model(
                'hf-hub:BVRA/MegaDescriptor-L-384',
                pretrained=pretrained,
                num_classes=0,  # 移除分类头
                global_pool='avg'
            )
            logger.info("成功加载 BVRA/MegaDescriptor-L-384 预训练模型")
        except Exception as e:
            logger.error(f"无法加载预训练模型: {e}")
            # 回退到标准模型
            self.backbone = timm.create_model(
                'vit_large_patch16_224',
                pretrained=pretrained,
                num_classes=0,
                global_pool='avg'
            )
            self.backbone_dim = 1024
            logger.warning("回退到 ViT-Large 模型")
        
        # 特征投影层 - 适配MegaDescriptor-L-384的输出
        self.feature_projector = nn.Sequential(
            nn.Linear(self.backbone_dim, feature_dim),
            nn.LayerNorm(feature_dim),
            nn.GELU(),
            nn.Dropout(dropout_rate),
            nn.Linear(feature_dim, feature_dim),
            nn.LayerNorm(feature_dim)
        )

        # 增强的分类头
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.LayerNorm(feature_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout_rate),
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.LayerNorm(feature_dim // 4),
            nn.GELU(),
            nn.Dropout(dropout_rate // 2),
            nn.Linear(feature_dim // 4, num_cats)
        )

        # 初始化权重
        self._init_weights()

        logger.info(f"MegaDescriptor-L-384模型初始化完成:")
        logger.info(f"  - 猫咪类别数: {num_cats}")
        logger.info(f"  - 骨干网络输出维度: {self.backbone_dim}")
        logger.info(f"  - 特征维度: {feature_dim}")
        logger.info(f"  - 模型类型: MegaDescriptor-L-384")
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.feature_projector, self.classifier]:
            for m in module.modules():
                if isinstance(m, nn.Linear):
                    nn.init.xavier_uniform_(m.weight)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
                elif isinstance(m, nn.LayerNorm):
                    nn.init.constant_(m.bias, 0)
                    nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x, return_features=False):
        """
        前向传播

        Args:
            x: 输入图像 [batch_size, 3, 384, 384] (MegaDescriptor-L-384使用384x384)
            return_features: 是否返回特征向量

        Returns:
            如果return_features=True: (logits, features)
            否则: logits
        """
        # MegaDescriptor特征提取
        backbone_features = self.backbone(x)  # [batch_size, backbone_dim]

        # 特征投影
        features = self.feature_projector(backbone_features)  # [batch_size, feature_dim]

        # 分类
        logits = self.classifier(features)  # [batch_size, num_cats]

        if return_features:
            return logits, features
        return logits

    def extract_features(self, x):
        """提取特征向量"""
        with torch.no_grad():
            backbone_features = self.backbone(x)
            features = self.feature_projector(backbone_features)
            return F.normalize(features, p=2, dim=1)
    
    def freeze_backbone(self, freeze=True):
        """冻结/解冻骨干网络"""
        for param in self.backbone.parameters():
            param.requires_grad = not freeze

        if freeze:
            logger.info("MegaDescriptor骨干网络已冻结")
        else:
            logger.info("MegaDescriptor骨干网络已解冻")

    def get_model_info(self):
        """获取模型信息"""
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)

        return {
            'model_type': 'MegaDescriptor-L-384',
            'num_cats': self.num_cats,
            'feature_dim': self.feature_dim,
            'backbone_dim': self.backbone_dim,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'input_size': '384x384',
            'pretrained': True
        }

class MegaDescriptorLargeTrainer:
    """MegaDescriptor-L-384模型训练器"""
    
    def __init__(self, model, device='cuda'):
        self.model = model.to(device)
        self.device = device
        
        # 优化器配置
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=1e-4,
            weight_decay=0.01,
            betas=(0.9, 0.999)
        )
        
        # 学习率调度器
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer,
            T_max=100,
            eta_min=1e-6
        )
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss(label_smoothing=0.1)
        
        logger.info("MegaDescriptor-L-384训练器初始化完成")
    
    def train_step(self, batch):
        """单步训练"""
        self.model.train()
        
        pixel_values = batch['pixel_values'].to(self.device)
        labels = batch['labels'].to(self.device)
        
        # 前向传播
        logits = self.model(pixel_values)
        loss = self.criterion(logits, labels)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
        
        self.optimizer.step()
        
        # 计算准确率
        with torch.no_grad():
            predictions = torch.argmax(logits, dim=1)
            accuracy = (predictions == labels).float().mean()
        
        return {
            'loss': loss.item(),
            'accuracy': accuracy.item(),
            'lr': self.optimizer.param_groups[0]['lr']
        }
    
    def validate(self, dataloader):
        """验证"""
        self.model.eval()
        
        total_loss = 0
        total_accuracy = 0
        num_batches = 0
        
        with torch.no_grad():
            for batch in dataloader:
                pixel_values = batch['pixel_values'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                logits = self.model(pixel_values)
                loss = self.criterion(logits, labels)
                
                predictions = torch.argmax(logits, dim=1)
                accuracy = (predictions == labels).float().mean()
                
                total_loss += loss.item()
                total_accuracy += accuracy.item()
                num_batches += 1
        
        return {
            'val_loss': total_loss / num_batches,
            'val_accuracy': total_accuracy / num_batches
        }
    
    def save_model(self, path):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'model_info': self.model.get_model_info()
        }, path)
        logger.info(f"模型已保存: {path}")
    
    def load_model(self, path):
        """加载模型"""
        checkpoint = torch.load(path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        logger.info(f"模型已加载: {path}")
        return checkpoint.get('model_info', {})

def create_megadescriptor_large_model(num_cats=50, feature_dim=1536, pretrained=True):
    """创建MegaDescriptor-L-384模型"""
    model = MegaDescriptorLargeCatModel(
        num_cats=num_cats,
        feature_dim=feature_dim,
        dropout_rate=0.1,
        pretrained=pretrained
    )

    return model

if __name__ == "__main__":
    # 测试模型
    model = create_megadescriptor_large_model(num_cats=10, feature_dim=1536)

    # 测试输入 (MegaDescriptor-L-384使用384x384输入)
    batch_size = 4
    input_tensor = torch.randn(batch_size, 3, 384, 384)

    # 前向传播测试
    logits, features = model(input_tensor, return_features=True)

    print(f"输入形状: {input_tensor.shape}")
    print(f"输出logits形状: {logits.shape}")
    print(f"特征向量形状: {features.shape}")
    print(f"模型信息: {model.get_model_info()}")
