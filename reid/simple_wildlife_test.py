#!/usr/bin/env python3
"""
简化的Wildlife-tools模型测试
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torchvision.transforms as T
import timm

# wildlife-tools imports
from wildlife_tools.data import ImageDataset
from wildlife_tools.features import DeepFeatures
from wildlife_tools.similarity import CosineSimilarity

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_test():
    """简化测试"""
    
    # 加载数据集分割信息
    with open('dataset_split.json', 'r') as f:
        split_info = json.load(f)
    
    # 加载模型
    model_path = 'wildlife_gpu_optimized.pth'
    checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
    
    # 创建骨干网络
    backbone = timm.create_model(
        'hf-hub:BVRA/MegaDescriptor-T-224',
        num_classes=0,
        pretrained=False
    )
    
    # 加载训练好的权重
    backbone.load_state_dict(checkpoint['backbone_state_dict'])
    backbone.eval()
    
    logger.info("模型加载成功")
    
    # 随机选择一些测试猫咪
    test_cats = random.sample(split_info['test_data'], 10)
    train_cats = random.sample(split_info['train_data'], 20)
    
    logger.info(f"选择了 {len(train_cats)} 只训练猫咪和 {len(test_cats)} 只测试猫咪")
    
    # 创建简单的数据集
    def create_simple_dataset(cats_data, root_path='../dataset/cat_individual_images'):
        rows = []
        for cat in cats_data:
            cat_id = cat['cat_id']
            images = cat['images'][:5]  # 只取前5张图片
            
            for img_path in images:
                relative_path = os.path.relpath(img_path, root_path)
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        df = pd.DataFrame(rows)
        
        transform = T.Compose([
            T.Resize([224, 224]),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        return ImageDataset(df, root_path, transform=transform)
    
    # 创建数据集
    train_dataset = create_simple_dataset(train_cats)
    test_dataset = create_simple_dataset(test_cats)
    
    logger.info(f"训练数据集: {len(train_dataset)} 张图片")
    logger.info(f"测试数据集: {len(test_dataset)} 张图片")
    
    # 提取特征
    extractor = DeepFeatures(backbone, batch_size=8)
    
    logger.info("提取训练集特征...")
    train_features = extractor(train_dataset)
    
    logger.info("提取测试集特征...")
    test_features = extractor(test_dataset)
    
    # 计算相似度
    logger.info("计算相似度...")
    similarity_function = CosineSimilarity()
    similarity_matrix = similarity_function(test_features, train_features)
    
    # 简单的最近邻分类
    logger.info("进行分类...")
    
    # 获取最高相似度的索引
    best_matches = np.argmax(similarity_matrix, axis=1)
    
    # 计算准确率
    correct = 0
    total = len(test_dataset)
    
    for i in range(total):
        test_label = test_dataset.labels_string[i]
        train_match_idx = best_matches[i]
        predicted_label = train_dataset.labels_string[train_match_idx]
        similarity_score = similarity_matrix[i, train_match_idx]
        
        is_correct = test_label == predicted_label
        if is_correct:
            correct += 1
        
        status = "✅" if is_correct else "❌"
        logger.info(f"{status} 真实:{test_label} 预测:{predicted_label} 相似度:{similarity_score:.3f}")
    
    accuracy = correct / total
    
    print(f"\n🎉 Wildlife-tools模型简化测试结果:")
    print(f"📊 准确率: {accuracy:.1%} ({correct}/{total})")
    print(f"📊 测试规模: {len(train_cats)} 只训练猫咪, {len(test_cats)} 只测试猫咪")
    
    return accuracy

if __name__ == "__main__":
    try:
        accuracy = simple_test()
        print(f"\n🎯 最终结果: {accuracy:.1%}")
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
