def get_optimized_adaptive_threshold(num_cats: int) -> float:
    """优化后的自适应阈值函数"""
    # 基于实际测试数据优化的阈值
    scale_thresholds = {
        5: 0.850,
        10: 0.830,
        20: 0.880,
    }
    
    # 如果正好匹配某个规模
    if num_cats in scale_thresholds:
        return scale_thresholds[num_cats]
    
    # 线性插值
    sorted_scales = sorted(scale_thresholds.keys())
    
    if num_cats <= sorted_scales[0]:
        return scale_thresholds[sorted_scales[0]]
    elif num_cats >= sorted_scales[-1]:
        return scale_thresholds[sorted_scales[-1]]
    else:
        # 找到相邻的两个规模进行插值
        for i in range(len(sorted_scales) - 1):
            if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                x1, y1 = sorted_scales[i], scale_thresholds[sorted_scales[i]]
                x2, y2 = sorted_scales[i + 1], scale_thresholds[sorted_scales[i + 1]]
                
                # 线性插值
                return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
    
    # 默认值
    return 0.65