#!/usr/bin/env python3
"""
独立测试集验证器 - 使用完全独立的测试集验证模型性能
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraFineMegaDescriptor(nn.Module):
    """超精细MegaDescriptor - 与训练时相同的架构"""
    
    def __init__(self, feature_dim=2048):
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0,
            features_only=True
        )
        
        # 获取多层特征维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            features = self.backbone(dummy_input)
            self.feature_dims = [f.shape[1] for f in features]
        
        # 多尺度特征融合
        self.multi_scale_fusion = nn.ModuleList([
            nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Flatten(),
                nn.Linear(dim, feature_dim // 4),
                nn.BatchNorm1d(feature_dim // 4),
                nn.ReLU()
            ) for dim in self.feature_dims[-4:]
        ])
        
        # 特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.2),
            
            nn.Linear(feature_dim * 2, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.15),
            
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.GELU(),
            nn.Dropout(0.05),
            
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 自注意力机制
        self.self_attention = nn.MultiheadAttention(
            feature_dim, num_heads=64, dropout=0.1, batch_first=True
        )
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(feature_dim)
        
        # 置信度校准网络
        self.confidence_calibrator = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x, return_confidence=False):
        # 多层特征提取
        multi_features = self.backbone(x)
        
        # 多尺度特征融合
        fused_features = []
        for i, (feature_map, fusion_layer) in enumerate(zip(multi_features[-4:], self.multi_scale_fusion)):
            fused = fusion_layer(feature_map)
            fused_features.append(fused)
        
        # 拼接多尺度特征
        combined_features = torch.cat(fused_features, dim=1)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(combined_features)
        
        # 自注意力机制
        enhanced_unsqueezed = enhanced_features.unsqueeze(1)
        attended, _ = self.self_attention(
            enhanced_unsqueezed, enhanced_unsqueezed, enhanced_unsqueezed
        )
        attended = attended.squeeze(1)
        
        # 残差连接和层归一化
        final_features = self.layer_norm(enhanced_features + attended)
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(final_features, p=2, dim=1)
        
        if return_confidence:
            # 置信度校准
            confidence = self.confidence_calibrator(final_features)
            return normalized_features, confidence
        else:
            return normalized_features

class IndependentValidator:
    """独立验证器 - 使用完全独立的测试集"""
    
    def __init__(self, model_path: str, split_file: str = 'dataset_split.json', device='cpu'):
        self.model_path = model_path
        self.split_file = split_file
        self.device = device
        
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            self.split_info = json.load(f)
        
        # 获取测试集数据
        self.test_cats = self.split_info['test_data']
        self.test_cat_ids = [cat['cat_id'] for cat in self.test_cats]
        
        # 构建测试集图片字典
        self.test_cat_images = {}
        for cat in self.test_cats:
            self.test_cat_images[cat['cat_id']] = cat['images']
        
        # 加载模型
        self.model = self._load_model()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 相似度匹配器
        self.feature_database = {}
        
        # 阈值策略
        self.threshold_strategies = {
            'conservative': {
                5: 0.70, 10: 0.75, 20: 0.80, 50: 0.75
            },
            'balanced': {
                5: 0.65, 10: 0.70, 20: 0.75, 50: 0.70
            },
            'aggressive': {
                5: 0.75, 10: 0.80, 20: 0.85, 50: 0.80
            }
        }
        
        logger.info(f"独立验证器初始化完成")
        logger.info(f"测试集: {len(self.test_cat_ids)} 只猫咪")
        logger.info(f"模型: {model_path}")
    
    def _load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            feature_dim = checkpoint.get('feature_dim', 2048)
            
            model = UltraFineMegaDescriptor(feature_dim=feature_dim).to(self.device)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            logger.info(f"成功加载模型: 特征维度 {feature_dim}")
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def extract_features(self, image_path: str, return_confidence=False):
        """提取特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                if return_confidence:
                    features, confidence = self.model(image_tensor, return_confidence=True)
                    return features.cpu().numpy().flatten(), confidence.cpu().item()
                else:
                    features = self.model(image_tensor)
                    return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败 {image_path}: {e}")
            if return_confidence:
                return np.random.randn(2048).astype(np.float32), 0.0
            else:
                return np.random.randn(2048).astype(np.float32)
    
    def register_test_cats(self, cat_ids: List[str], train_ratio: float = 0.7):
        """注册测试集中的猫咪"""
        logger.info(f"注册 {len(cat_ids)} 只测试猫咪...")
        
        registered_cats = []
        
        for cat_id in cat_ids:
            if cat_id not in self.test_cat_images:
                logger.warning(f"猫咪 {cat_id} 不在测试集中")
                continue
            
            images = self.test_cat_images[cat_id]
            if len(images) < 5:
                logger.warning(f"猫咪 {cat_id} 图片数量不足: {len(images)}")
                continue
            
            # 分割注册和测试图片
            train_count = max(3, int(len(images) * train_ratio))
            train_images = images[:train_count]
            test_images = images[train_count:]
            
            # 提取注册图片的特征
            cat_features = []
            for img_path in train_images:
                try:
                    features = self.extract_features(img_path)
                    cat_features.append(features)
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            if len(cat_features) >= 3:
                self.feature_database[cat_id] = cat_features
                registered_cats.append((cat_id, test_images))
                logger.info(f"注册猫咪 {cat_id}: {len(cat_features)} 张训练图片, {len(test_images)} 张测试图片")
        
        logger.info(f"成功注册 {len(registered_cats)} 只猫咪")
        return registered_cats
    
    def compute_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算相似度"""
        if cat_id not in self.feature_database:
            return 0.0
        
        cat_features_list = self.feature_database[cat_id]
        
        similarities = []
        for cat_features in cat_features_list:
            sim = np.dot(query_features, cat_features)
            similarities.append(sim)
        
        if not similarities:
            return 0.0
        
        # 使用加权平均
        similarities = np.array(similarities)
        sorted_sims = np.sort(similarities)[::-1]
        
        if len(sorted_sims) >= 3:
            weights = np.array([0.5, 0.3, 0.2])
            weighted_sim = np.average(sorted_sims[:3], weights=weights)
        elif len(sorted_sims) == 2:
            weights = np.array([0.7, 0.3])
            weighted_sim = np.average(sorted_sims[:2], weights=weights)
        else:
            weighted_sim = sorted_sims[0]
        
        return float(weighted_sim)
    
    def get_adaptive_threshold(self, num_cats: int, strategy: str = 'balanced') -> float:
        """获取自适应阈值"""
        thresholds = self.threshold_strategies[strategy]
        
        # 线性插值
        sorted_scales = sorted(thresholds.keys())
        
        if num_cats <= sorted_scales[0]:
            return thresholds[sorted_scales[0]]
        elif num_cats >= sorted_scales[-1]:
            return thresholds[sorted_scales[-1]]
        else:
            for i in range(len(sorted_scales) - 1):
                if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                    x1, y1 = sorted_scales[i], thresholds[sorted_scales[i]]
                    x2, y2 = sorted_scales[i + 1], thresholds[sorted_scales[i + 1]]
                    return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
        
        return 0.70  # 默认值
    
    def recognize_cat(self, image_path: str, strategy: str = 'balanced'):
        """识别猫咪"""
        try:
            # 提取特征和置信度
            query_features, raw_confidence = self.extract_features(image_path, return_confidence=True)
            
            # 计算与所有注册猫咪的相似度
            matches = []
            for cat_id in self.feature_database:
                similarity = self.compute_similarity(query_features, cat_id)
                matches.append((cat_id, similarity))
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            # 排序找到最佳匹配
            matches.sort(key=lambda x: x[1], reverse=True)
            best_match_id, best_similarity = matches[0]
            
            # 获取自适应阈值
            num_cats = len(self.feature_database)
            adaptive_threshold = self.get_adaptive_threshold(num_cats, strategy)
            
            # 校准置信度
            calibrated_confidence = min(0.99, best_similarity * raw_confidence * 1.1)
            
            if best_similarity >= adaptive_threshold:
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'confidence': calibrated_confidence,
                    'similarity': best_similarity,
                    'raw_confidence': raw_confidence,
                    'threshold_used': adaptive_threshold,
                    'strategy_used': strategy,
                    'status': 'recognized'
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'best_match': {
                        'cat_id': best_match_id,
                        'similarity': best_similarity,
                        'raw_confidence': raw_confidence
                    },
                    'threshold_used': adaptive_threshold,
                    'strategy_used': strategy
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_independent_test_set(self, num_cats: int = 20, strategy: str = 'balanced', rounds: int = 5):
        """验证独立测试集"""
        logger.info(f"🔍 独立测试集验证: {num_cats} 只猫咪, {rounds} 轮")
        
        all_results = []
        
        for round_num in range(rounds):
            logger.info(f"第 {round_num + 1} 轮...")
            
            # 随机选择测试猫咪
            if len(self.test_cat_ids) < num_cats:
                logger.warning(f"测试集猫咪数量不足: 需要{num_cats}只，实际{len(self.test_cat_ids)}只")
                selected_cats = self.test_cat_ids
            else:
                selected_cats = random.sample(self.test_cat_ids, num_cats)
            
            # 注册猫咪
            registered_cats = self.register_test_cats(selected_cats)
            
            if len(registered_cats) < 3:
                logger.warning(f"注册成功的猫咪数量不足: {len(registered_cats)}")
                continue
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            similarities = []
            raw_confidences = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                result = self.recognize_cat(test_image, strategy)
                
                total += 1
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                
                if is_correct:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                similarities.append(result.get('similarity', 0.0))
                raw_confidences.append(result.get('raw_confidence', 0.0))
                
                # 显示结果
                status = "✅" if is_correct else "❌"
                predicted = result.get('cat_id', 'unknown')
                similarity = result.get('similarity', 0.0)
                confidence = result.get('confidence', 0.0)
                raw_conf = result.get('raw_confidence', 0.0)
                
                logger.info(f"{status} 真实:{cat_id} 预测:{predicted} "
                          f"相似度:{similarity:.3f} 置信度:{confidence:.1%} 原始:{raw_conf:.3f}")
            
            # 计算轮次统计
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            avg_raw_confidence = np.mean(raw_confidences) if raw_confidences else 0.0
            
            round_result = {
                'round': round_num + 1,
                'accuracy': accuracy,
                'correct': correct,
                'total': total,
                'avg_confidence': avg_confidence,
                'avg_similarity': avg_similarity,
                'avg_raw_confidence': avg_raw_confidence,
                'selected_cats': selected_cats
            }
            
            all_results.append(round_result)
            
            logger.info(f"轮次结果: 准确率 {accuracy:.1%} ({correct}/{total})")
            
            # 清空特征数据库为下一轮做准备
            self.feature_database.clear()
        
        return all_results
    
    def display_validation_summary(self, results: List[Dict], num_cats: int):
        """显示验证总结"""
        if not results:
            print("没有有效的测试结果")
            return
        
        accuracies = [r['accuracy'] for r in results]
        confidences = [r['avg_confidence'] for r in results]
        similarities = [r['avg_similarity'] for r in results]
        raw_confidences = [r['avg_raw_confidence'] for r in results]
        
        print(f"\n" + "=" * 80)
        print(f"🎯 独立测试集验证总结 ({num_cats} 只猫咪)")
        print("=" * 80)
        
        print(f"📊 性能统计:")
        print(f"   平均准确率: {np.mean(accuracies):.1%} ± {np.std(accuracies):.1%}")
        print(f"   准确率范围: {np.min(accuracies):.1%} - {np.max(accuracies):.1%}")
        print(f"   平均置信度: {np.mean(confidences):.1%}")
        print(f"   平均相似度: {np.mean(similarities):.3f}")
        print(f"   平均原始置信度: {np.mean(raw_confidences):.3f}")
        
        # 达标率
        target_95_count = sum(1 for acc in accuracies if acc >= 0.95)
        target_90_count = sum(1 for acc in accuracies if acc >= 0.90)
        
        print(f"\n🎯 目标达成:")
        print(f"   达到95%+: {target_95_count}/{len(accuracies)} ({target_95_count/len(accuracies):.1%})")
        print(f"   达到90%+: {target_90_count}/{len(accuracies)} ({target_90_count/len(accuracies):.1%})")
        
        # 最终评价
        avg_accuracy = np.mean(accuracies)
        if avg_accuracy >= 0.95:
            rating = "🌟 优秀 - 达到95%+目标"
        elif avg_accuracy >= 0.90:
            rating = "✅ 良好 - 达到90%+目标"
        elif avg_accuracy >= 0.85:
            rating = "📈 不错 - 接近目标"
        else:
            rating = "⚠️ 需要改进"
        
        print(f"\n📊 最终评价: {rating}")
        
        return {
            'avg_accuracy': np.mean(accuracies),
            'accuracy_std': np.std(accuracies),
            'target_95_rate': target_95_count / len(accuracies),
            'target_90_rate': target_90_count / len(accuracies)
        }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='独立测试集验证')
    parser.add_argument('--model', type=str, required=True,
                       help='模型路径')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--cats', type=int, default=20,
                       help='测试猫咪数量')
    parser.add_argument('--rounds', type=int, default=5,
                       help='测试轮数')
    parser.add_argument('--strategy', type=str, default='balanced',
                       choices=['conservative', 'balanced', 'aggressive'],
                       help='阈值策略')
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = IndependentValidator(args.model, args.split_file, device='cpu')
    
    # 运行验证
    results = validator.validate_independent_test_set(
        num_cats=args.cats,
        strategy=args.strategy,
        rounds=args.rounds
    )
    
    # 显示总结
    summary = validator.display_validation_summary(results, args.cats)
    
    print(f"\n🎉 独立验证完成!")

if __name__ == "__main__":
    main()
