#!/usr/bin/env python3
"""
Wildlife-tools模型评估器 - 修复版本
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torchvision.transforms as T
import timm

# wildlife-tools imports
from wildlife_tools.data import ImageDataset
from wildlife_tools.features import DeepFeatures
from wildlife_tools.similarity import CosineSimilarity
from wildlife_tools.inference import KnnClassifier

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WildlifeModelEvaluator:
    """Wildlife-tools模型评估器"""
    
    def __init__(self, model_path: str, split_file: str = 'dataset_split.json', device='cpu'):
        self.model_path = model_path
        self.split_file = split_file
        self.device = device
        
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            self.split_info = json.load(f)
        
        # 加载模型
        self.backbone = self._load_model()
        
        # 创建数据集
        self.train_dataset = self._create_dataset(use_train_set=True)
        self.test_dataset = self._create_dataset(use_train_set=False)
        
        logger.info(f"Wildlife模型评估器初始化完成")
        logger.info(f"  模型: {model_path}")
        logger.info(f"  设备: {device}")
        logger.info(f"  训练集: {len(self.train_dataset)} 张图片")
        logger.info(f"  测试集: {len(self.test_dataset)} 张图片")
    
    def _load_model(self):
        """加载模型"""
        try:
            checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
            
            # 创建骨干网络
            backbone = timm.create_model(
                'hf-hub:BVRA/MegaDescriptor-T-224',
                num_classes=0,
                pretrained=False  # 不加载预训练权重，使用我们训练的权重
            )
            
            # 加载训练好的权重
            backbone.load_state_dict(checkpoint['backbone_state_dict'])
            backbone = backbone.to(self.device)
            backbone.eval()
            
            logger.info(f"成功加载Wildlife模型")
            return backbone
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            raise
    
    def _create_dataset(self, use_train_set: bool = True):
        """创建数据集"""
        cats_data = self.split_info['train_data'] if use_train_set else self.split_info['test_data']
        
        # 创建DataFrame
        rows = []
        for cat in cats_data:
            cat_id = cat['cat_id']
            images = cat['images']
            
            for img_path in images:
                relative_path = os.path.relpath(img_path, '../dataset/cat_individual_images')
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        df = pd.DataFrame(rows)
        root_path = '../dataset/cat_individual_images'
        
        # 测试时不使用数据增强
        transform = T.Compose([
            T.Resize([224, 224]),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        dataset = ImageDataset(df, root_path, transform=transform)
        
        dataset_type = "训练集" if use_train_set else "测试集"
        logger.info(f"{dataset_type}创建完成: {len(dataset)} 张图片, {dataset.num_classes} 个类别")
        
        return dataset
    
    def evaluate_full_test_set(self):
        """在完整测试集上评估"""
        logger.info("开始完整测试集评估...")
        
        # 创建特征提取器
        extractor = DeepFeatures(self.backbone, batch_size=16)
        
        # 提取训练集特征
        logger.info("提取训练集特征...")
        train_features = extractor(self.train_dataset)
        
        # 清理内存
        if self.device == 'cuda':
            torch.cuda.empty_cache()
        
        # 提取测试集特征
        logger.info("提取测试集特征...")
        test_features = extractor(self.test_dataset)
        
        # 计算相似度
        logger.info("计算相似度...")
        similarity_function = CosineSimilarity()
        similarity = similarity_function(test_features, train_features)
        
        # 进行分类 - 修复索引问题
        logger.info("进行分类...")
        classifier = KnnClassifier(k=1, database_labels=self.train_dataset.labels_string)
        
        # 获取余弦相似度矩阵
        cosine_similarity = similarity['cosine']
        predictions = classifier(cosine_similarity)
        
        # 计算准确率
        true_labels = self.test_dataset.labels_string
        accuracy = np.mean(true_labels == predictions)
        
        logger.info(f"完整测试集评估完成!")
        logger.info(f"测试集准确率: {accuracy:.1%}")
        
        return {
            'accuracy': accuracy,
            'predictions': predictions,
            'true_labels': true_labels,
            'total_samples': len(true_labels),
            'correct_samples': int(np.sum(true_labels == predictions))
        }
    
    def evaluate_random_subset(self, num_cats: int = 20, rounds: int = 5):
        """随机子集评估"""
        logger.info(f"开始随机子集评估: {num_cats} 只猫咪, {rounds} 轮")
        
        all_results = []
        
        for round_num in range(rounds):
            logger.info(f"第 {round_num + 1} 轮...")
            
            # 随机选择测试猫咪
            test_cat_ids = list(set([cat['cat_id'] for cat in self.split_info['test_data']]))
            if len(test_cat_ids) < num_cats:
                selected_cats = test_cat_ids
            else:
                selected_cats = random.sample(test_cat_ids, num_cats)
            
            # 创建子集数据
            train_subset = self._create_subset_dataset(selected_cats, use_train_set=True)
            test_subset = self._create_subset_dataset(selected_cats, use_train_set=False)
            
            if len(train_subset) == 0 or len(test_subset) == 0:
                logger.warning(f"第 {round_num + 1} 轮数据不足，跳过")
                continue
            
            # 评估子集
            try:
                extractor = DeepFeatures(self.backbone, batch_size=8)
                
                train_features = extractor(train_subset)
                test_features = extractor(test_subset)
                
                similarity_function = CosineSimilarity()
                similarity = similarity_function(test_features, train_features)
                
                classifier = KnnClassifier(k=1, database_labels=train_subset.labels_string)
                predictions = classifier(similarity['cosine'])
                
                accuracy = np.mean(test_subset.labels_string == predictions)
                
                round_result = {
                    'round': round_num + 1,
                    'accuracy': accuracy,
                    'num_cats': len(selected_cats),
                    'test_samples': len(test_subset),
                    'correct': int(np.sum(test_subset.labels_string == predictions))
                }
                
                all_results.append(round_result)
                
                logger.info(f"第 {round_num + 1} 轮结果: 准确率 {accuracy:.1%} ({round_result['correct']}/{round_result['test_samples']})")
                
            except Exception as e:
                logger.error(f"第 {round_num + 1} 轮评估失败: {e}")
                continue
        
        if all_results:
            accuracies = [r['accuracy'] for r in all_results]
            avg_accuracy = np.mean(accuracies)
            accuracy_std = np.std(accuracies)
            
            logger.info(f"随机子集评估完成!")
            logger.info(f"平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
            logger.info(f"准确率范围: {np.min(accuracies):.1%} - {np.max(accuracies):.1%}")
            
            return {
                'avg_accuracy': avg_accuracy,
                'accuracy_std': accuracy_std,
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'results': all_results
            }
        else:
            logger.error("没有有效的评估结果")
            return None
    
    def _create_subset_dataset(self, selected_cats: List[str], use_train_set: bool = True):
        """创建子集数据集"""
        cats_data = self.split_info['train_data'] if use_train_set else self.split_info['test_data']
        
        # 过滤选中的猫咪
        filtered_cats = [cat for cat in cats_data if cat['cat_id'] in selected_cats]
        
        if not filtered_cats:
            return []
        
        # 创建DataFrame
        rows = []
        for cat in filtered_cats:
            cat_id = cat['cat_id']
            images = cat['images']
            
            for img_path in images:
                relative_path = os.path.relpath(img_path, '../dataset/cat_individual_images')
                rows.append({
                    'path': relative_path,
                    'identity': cat_id,
                    'species': 'cat',
                    'viewpoint': 'unknown',
                    'bbox': None
                })
        
        if not rows:
            return []
        
        df = pd.DataFrame(rows)
        root_path = '../dataset/cat_individual_images'
        
        transform = T.Compose([
            T.Resize([224, 224]),
            T.ToTensor(),
            T.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        dataset = ImageDataset(df, root_path, transform=transform)
        return dataset

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Wildlife模型评估')
    parser.add_argument('--model', type=str, default='wildlife_gpu_optimized.pth',
                       help='模型路径')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--device', type=str, default='cpu',
                       help='设备')
    parser.add_argument('--full-test', action='store_true',
                       help='进行完整测试集评估')
    parser.add_argument('--random-test', action='store_true',
                       help='进行随机子集评估')
    parser.add_argument('--cats', type=int, default=20,
                       help='随机测试的猫咪数量')
    parser.add_argument('--rounds', type=int, default=5,
                       help='随机测试轮数')
    
    args = parser.parse_args()
    
    try:
        # 创建评估器
        evaluator = WildlifeModelEvaluator(args.model, args.split_file, args.device)
        
        results = {}
        
        # 完整测试集评估
        if args.full_test:
            full_results = evaluator.evaluate_full_test_set()
            results['full_test'] = full_results
            
            print(f"\n🎉 完整测试集评估结果:")
            print(f"📊 准确率: {full_results['accuracy']:.1%}")
            print(f"📊 正确样本: {full_results['correct_samples']}/{full_results['total_samples']}")
        
        # 随机子集评估
        if args.random_test:
            random_results = evaluator.evaluate_random_subset(args.cats, args.rounds)
            if random_results:
                results['random_test'] = random_results
                
                print(f"\n🎯 随机子集评估结果:")
                print(f"📊 平均准确率: {random_results['avg_accuracy']:.1%} ± {random_results['accuracy_std']:.1%}")
                print(f"📊 准确率范围: {random_results['min_accuracy']:.1%} - {random_results['max_accuracy']:.1%}")
        
        # 保存结果
        if results:
            output_path = args.model.replace('.pth', '_evaluation_results.json')
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"评估结果已保存: {output_path}")
        
        print(f"\n🎉 Wildlife-tools模型评估完成!")
        
    except Exception as e:
        logger.error(f"评估失败: {e}")
        raise

if __name__ == "__main__":
    main()
