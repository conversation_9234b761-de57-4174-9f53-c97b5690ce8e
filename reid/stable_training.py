#!/usr/bin/env python3
"""
稳定训练版本 - 修复震荡问题，确保稳定收敛
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import random
import time
import logging
import json
from typing import Dict, List, Tuple
from PIL import Image
import torchvision.transforms as transforms
from collections import defaultdict
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StableTripletDataset(Dataset):
    """稳定的三元组数据集 - 减少困难样本比例"""
    
    def __init__(self, split_file: str = 'dataset_split.json', use_train_set: bool = True):
        # 加载数据集分割信息
        with open(split_file, 'r') as f:
            split_info = json.load(f)
        
        self.cats_data = split_info['train_data'] if use_train_set else split_info['test_data']
        self.cat_ids = [cat['cat_id'] for cat in self.cats_data]
        
        # 构建猫咪图片字典
        self.cat_images = {}
        for cat in self.cats_data:
            self.cat_images[cat['cat_id']] = cat['images']
        
        # 温和的数据增强
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 计算困难负样本对
        self.hard_negative_pairs = self._compute_hard_negatives()
        
        dataset_type = "训练集" if use_train_set else "测试集"
        logger.info(f"稳定数据集构建完成 ({dataset_type}): {len(self.cat_ids)} 只猫咪")
        logger.info(f"总图片数: {sum(len(imgs) for imgs in self.cat_images.values())}")
        logger.info(f"困难负样本对: {len(self.hard_negative_pairs)}")
    
    def _compute_hard_negatives(self):
        """计算困难负样本对"""
        hard_pairs = []
        
        # 基于ID数值相近性
        for i, cat_id1 in enumerate(self.cat_ids):
            for j, cat_id2 in enumerate(self.cat_ids[i+1:], i+1):
                id_diff = abs(int(cat_id1) - int(cat_id2))
                
                # ID相近的猫咪更可能在视觉上相似
                if id_diff < 50:  # 更严格的阈值
                    hard_pairs.append((cat_id1, cat_id2))
        
        return hard_pairs
    
    def __len__(self):
        return len(self.cat_ids) * 80  # 减少样本数
    
    def __getitem__(self, idx):
        """生成三元组 - 降低困难样本比例"""
        # 只有50%概率使用困难负样本
        use_hard_negative = random.random() < 0.5 and self.hard_negative_pairs
        
        # 选择anchor猫咪
        anchor_cat = random.choice(self.cat_ids)
        
        # 选择positive
        anchor_img = random.choice(self.cat_images[anchor_cat])
        positive_img = random.choice(self.cat_images[anchor_cat])
        while positive_img == anchor_img and len(self.cat_images[anchor_cat]) > 1:
            positive_img = random.choice(self.cat_images[anchor_cat])
        
        # 选择negative
        if use_hard_negative:
            hard_pairs_for_anchor = [pair for pair in self.hard_negative_pairs 
                                   if anchor_cat in pair]
            if hard_pairs_for_anchor:
                pair = random.choice(hard_pairs_for_anchor)
                negative_cat = pair[1] if pair[0] == anchor_cat else pair[0]
            else:
                negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        else:
            negative_cat = random.choice([c for c in self.cat_ids if c != anchor_cat])
        
        negative_img = random.choice(self.cat_images[negative_cat])
        
        try:
            anchor = self.transform(Image.open(anchor_img).convert('RGB'))
            positive = self.transform(Image.open(positive_img).convert('RGB'))
            negative = self.transform(Image.open(negative_img).convert('RGB'))
            
            return anchor, positive, negative
        except Exception as e:
            return self.__getitem__(random.randint(0, len(self) - 1))

class StableMegaDescriptor(nn.Module):
    """稳定的MegaDescriptor - 简化架构，确保稳定训练"""
    
    def __init__(self, feature_dim=1536):  # 适中的维度
        super().__init__()
        
        # MegaDescriptor骨干网络
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 简化的特征增强网络
        self.feature_enhancer = nn.Sequential(
            # 第一层：扩展
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 第二层：处理
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.05),
            
            # 输出层
            nn.Linear(feature_dim, feature_dim)
        )
        
        # 简化的置信度网络
        self.confidence_head = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 4),
            nn.ReLU(),
            nn.Linear(feature_dim // 4, 1),
            nn.Sigmoid()
        )
        
        logger.info(f"稳定MegaDescriptor初始化: {backbone_dim} -> {feature_dim}")
    
    def forward(self, x, return_confidence=False):
        # 骨干网络特征
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = torch.nn.functional.normalize(enhanced_features, p=2, dim=1)
        
        if return_confidence:
            # 置信度预测
            confidence = self.confidence_head(enhanced_features)
            return normalized_features, confidence
        else:
            return normalized_features

class StableTrainer:
    """稳定训练器 - 保守的训练策略"""
    
    def __init__(self, split_file: str = 'dataset_split.json'):
        self.split_file = split_file
        self.device = 'cpu'  # 强制使用CPU避免内存问题
        
        # 数据集和数据加载器
        self.dataset = StableTripletDataset(split_file, use_train_set=True)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=8,   # CPU训练使用较小批次
            shuffle=True, 
            num_workers=2,  # CPU训练减少worker数
            pin_memory=True,
            persistent_workers=True
        )
        
        # 模型
        self.model = StableMegaDescriptor(feature_dim=1536).to(self.device)
        
        # 简单的三元组损失
        self.triplet_loss = nn.TripletMarginLoss(margin=0.5)
        self.confidence_loss = nn.MSELoss()
        
        # 保守的优化器
        self.optimizer = optim.Adam([
            {'params': self.model.backbone.parameters(), 'lr': 5e-6},
            {'params': self.model.feature_enhancer.parameters(), 'lr': 1e-4},
            {'params': self.model.confidence_head.parameters(), 'lr': 5e-4}
        ], weight_decay=1e-5)
        
        # 稳定的学习率调度
        self.scheduler = optim.lr_scheduler.StepLR(
            self.optimizer, step_size=3, gamma=0.7
        )
        
        logger.info(f"稳定训练器初始化完成: {len(self.dataset.cat_ids)} 只猫咪")
        logger.info(f"批次数/轮: {len(self.dataloader)}")
    
    def train_epoch(self, epoch: int):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        triplet_losses = []
        confidence_losses = []
        num_batches = 0
        
        for batch_idx, (anchor, positive, negative) in enumerate(self.dataloader):
            anchor = anchor.to(self.device)
            positive = positive.to(self.device)
            negative = negative.to(self.device)
            
            # 前向传播
            anchor_feat, anchor_conf = self.model(anchor, return_confidence=True)
            positive_feat, positive_conf = self.model(positive, return_confidence=True)
            negative_feat, negative_conf = self.model(negative, return_confidence=True)
            
            # 三元组损失
            triplet_loss = self.triplet_loss(anchor_feat, positive_feat, negative_feat)
            
            # 置信度损失 - 简化版本
            pos_dist = torch.norm(anchor_feat - positive_feat, p=2, dim=1)
            neg_dist = torch.norm(anchor_feat - negative_feat, p=2, dim=1)
            
            # 正样本应该有高置信度，负样本应该有低置信度
            pos_target = torch.exp(-pos_dist * 2).unsqueeze(1)
            neg_target = torch.exp(-neg_dist).unsqueeze(1)
            
            conf_loss = (
                self.confidence_loss(anchor_conf, pos_target) +
                self.confidence_loss(positive_conf, pos_target) +
                self.confidence_loss(negative_conf, neg_target)
            ) / 3
            
            # 总损失
            total_batch_loss = triplet_loss + 0.1 * conf_loss
            
            # 反向传播
            self.optimizer.zero_grad()
            total_batch_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=0.5)
            
            self.optimizer.step()
            
            total_loss += total_batch_loss.item()
            triplet_losses.append(triplet_loss.item())
            confidence_losses.append(conf_loss.item())
            num_batches += 1
            
            if batch_idx % 100 == 0:
                current_lr = self.optimizer.param_groups[1]['lr']
                logger.info(f"Epoch {epoch}, Batch {batch_idx}: "
                          f"Total={total_batch_loss.item():.4f}, "
                          f"Triplet={triplet_loss.item():.4f}, "
                          f"Conf={conf_loss.item():.4f}, "
                          f"LR={current_lr:.2e}")
        
        # 更新学习率
        self.scheduler.step()
        
        return {
            'total_loss': total_loss / num_batches,
            'triplet_loss': np.mean(triplet_losses),
            'confidence_loss': np.mean(confidence_losses)
        }
    
    def evaluate_separability(self):
        """评估特征分离度"""
        self.model.eval()
        
        eval_cats = random.sample(self.dataset.cat_ids, min(30, len(self.dataset.cat_ids)))
        intra_dists = []
        inter_dists = []
        confidences = []
        
        with torch.no_grad():
            for cat_id in eval_cats:
                images = self.dataset.cat_images[cat_id]
                
                # 类内距离
                if len(images) >= 2:
                    img1, img2 = random.sample(images, 2)
                    
                    tensor1 = self.dataset.transform(Image.open(img1).convert('RGB')).unsqueeze(0).to(self.device)
                    tensor2 = self.dataset.transform(Image.open(img2).convert('RGB')).unsqueeze(0).to(self.device)
                    
                    feat1, conf1 = self.model(tensor1, return_confidence=True)
                    feat2, conf2 = self.model(tensor2, return_confidence=True)
                    
                    intra_dist = torch.norm(feat1 - feat2, p=2).item()
                    intra_dists.append(intra_dist)
                    confidences.extend([conf1.item(), conf2.item()])
                
                # 类间距离
                other_cat = random.choice([c for c in eval_cats if c != cat_id])
                other_img = random.choice(self.dataset.cat_images[other_cat])
                
                tensor1 = self.dataset.transform(Image.open(random.choice(images)).convert('RGB')).unsqueeze(0).to(self.device)
                tensor2 = self.dataset.transform(Image.open(other_img).convert('RGB')).unsqueeze(0).to(self.device)
                
                feat1, _ = self.model(tensor1, return_confidence=True)
                feat2, _ = self.model(tensor2, return_confidence=True)
                
                inter_dist = torch.norm(feat1 - feat2, p=2).item()
                inter_dists.append(inter_dist)
        
        avg_intra = np.mean(intra_dists) if intra_dists else 0.0
        avg_inter = np.mean(inter_dists) if inter_dists else 0.0
        separability = avg_inter / (avg_intra + 1e-8)
        avg_confidence = np.mean(confidences) if confidences else 0.0
        
        return {
            'intra_distance': avg_intra,
            'inter_distance': avg_inter,
            'separability': separability,
            'avg_confidence': avg_confidence
        }
    
    def train(self, epochs: int = 10, save_path: str = 'stable_megadescriptor.pth'):
        """完整训练流程"""
        logger.info(f"开始稳定训练: {epochs} epochs, {len(self.dataset.cat_ids)} 只猫咪")
        
        best_separability = 0.0
        training_history = []
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            train_stats = self.train_epoch(epoch + 1)
            
            # 评估
            eval_stats = self.evaluate_separability()
            
            epoch_time = time.time() - start_time
            
            # 记录历史
            epoch_result = {
                'epoch': epoch + 1,
                'train_stats': train_stats,
                'eval_stats': eval_stats,
                'time': epoch_time
            }
            training_history.append(epoch_result)
            
            logger.info(f"Epoch {epoch + 1}/{epochs}:")
            logger.info(f"  总损失: {train_stats['total_loss']:.4f}")
            logger.info(f"  三元组损失: {train_stats['triplet_loss']:.4f}")
            logger.info(f"  置信度损失: {train_stats['confidence_loss']:.4f}")
            logger.info(f"  分离度: {eval_stats['separability']:.4f}")
            logger.info(f"  类内距离: {eval_stats['intra_distance']:.4f}")
            logger.info(f"  类间距离: {eval_stats['inter_distance']:.4f}")
            logger.info(f"  平均置信度: {eval_stats['avg_confidence']:.3f}")
            logger.info(f"  用时: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if eval_stats['separability'] > best_separability:
                best_separability = eval_stats['separability']
                self.save_model(save_path)
                logger.info(f"🎉 保存最佳模型: 分离度 {best_separability:.4f}")
        
        logger.info(f"🚀 稳定训练完成! 最佳分离度: {best_separability:.4f}")
        
        # 保存训练历史
        history_path = save_path.replace('.pth', '_history.json')
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        return best_separability, training_history
    
    def save_model(self, save_path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'feature_dim': 1536,
            'model_type': 'StableMegaDescriptor',
            'split_file': self.split_file
        }, save_path)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='稳定训练')
    parser.add_argument('--split-file', type=str, default='dataset_split.json',
                       help='数据集分割文件')
    parser.add_argument('--epochs', type=int, default=8,
                       help='训练轮数')
    parser.add_argument('--output', type=str, default='stable_megadescriptor_250cats.pth',
                       help='模型保存路径')
    
    args = parser.parse_args()
    
    # 创建训练器
    trainer = StableTrainer(split_file=args.split_file)
    
    # 开始训练
    best_sep, history = trainer.train(epochs=args.epochs, save_path=args.output)
    
    logger.info(f"🎉 稳定训练完成! 最佳分离度: {best_sep:.4f}")
    logger.info(f"📁 模型已保存: {args.output}")

if __name__ == "__main__":
    main()
