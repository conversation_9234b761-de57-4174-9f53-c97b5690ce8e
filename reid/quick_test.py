#!/usr/bin/env python3
"""
快速测试脚本 - 在训练期间进行基础功能验证
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_recognizer import create_enhanced_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def quick_test(dataset_path: str, model_path: str = None, num_cats: int = 3):
    """快速测试功能"""
    dataset_path = Path(dataset_path)
    
    print("🚀 快速功能测试")
    print("=" * 50)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    if len(available_cats) < num_cats:
        print(f"❌ 可用猫咪数量不足: {len(available_cats)} < {num_cats}")
        return
    
    print(f"📊 使用 {num_cats} 只猫咪进行测试")
    print(f"💾 模型路径: {model_path if model_path else '基础模型'}")
    
    # 创建识别器 - 使用CPU避免GPU内存冲突
    try:
        recognizer = create_enhanced_recognizer(model_path, device='cpu')
        print("✅ 识别器创建成功")
    except Exception as e:
        print(f"❌ 识别器创建失败: {e}")
        return
    
    # 选择测试猫咪
    selected_cats = random.sample(available_cats, num_cats)
    
    # 注册阶段
    print("\n📝 注册阶段")
    print("-" * 30)
    
    registered_cats = []
    for cat_id, image_paths in selected_cats:
        train_count = max(3, int(len(image_paths) * 0.7))
        train_images = image_paths[:train_count]
        test_images = image_paths[train_count:]
        
        print(f"注册猫咪 {cat_id}: 使用 {len(train_images)} 张图片...")
        
        start_time = time.time()
        result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
        reg_time = time.time() - start_time
        
        if result['success']:
            print(f"  ✅ 成功 ({reg_time:.2f}s)")
            if test_images:
                registered_cats.append((cat_id, test_images))
        else:
            print(f"  ❌ 失败: {result.get('error', '未知错误')}")
    
    if not registered_cats:
        print("❌ 没有成功注册的猫咪")
        return
    
    # 识别阶段
    print(f"\n🔍 识别阶段 ({len(registered_cats)} 只猫咪)")
    print("-" * 30)
    
    correct = 0
    total = 0
    response_times = []
    confidences = []
    similarities = []
    
    for cat_id, test_images in registered_cats:
        test_image = random.choice(test_images)
        print(f"识别猫咪 {cat_id}...")
        
        start_time = time.time()
        result = recognizer.recognize_cat(test_image)
        response_time = time.time() - start_time
        
        response_times.append(response_time)
        total += 1
        
        is_correct = result.get('success') and result.get('cat_id') == cat_id
        if is_correct:
            correct += 1
            confidences.append(result.get('confidence', 0.0))
        
        # 记录相似度
        if 'similarity' in result:
            similarities.append(result['similarity'])
        elif 'best_match' in result and 'similarity' in result['best_match']:
            similarities.append(result['best_match']['similarity'])
        
        # 显示结果
        status = "✅" if is_correct else "❌"
        confidence = result.get('confidence', 0.0)
        similarity = result.get('similarity', result.get('best_match', {}).get('similarity', 0.0))
        threshold = result.get('threshold_used', 0.0)
        predicted_id = result.get('cat_id', result.get('best_match', {}).get('cat_id', 'unknown'))
        
        print(f"  {status} 预测: {predicted_id}, 置信度: {confidence:.1%}, "
              f"相似度: {similarity:.3f}, 阈值: {threshold:.3f} ({response_time:.3f}s)")
    
    # 统计结果
    accuracy = correct / total if total > 0 else 0.0
    avg_confidence = np.mean(confidences) if confidences else 0.0
    avg_response_time = np.mean(response_times) if response_times else 0.0
    avg_similarity = np.mean(similarities) if similarities else 0.0
    
    print(f"\n📈 测试结果")
    print("-" * 30)
    print(f"准确率: {accuracy:.1%} ({correct}/{total})")
    print(f"平均置信度: {avg_confidence:.1%}")
    print(f"平均相似度: {avg_similarity:.3f}")
    print(f"平均响应时间: {avg_response_time:.3f}s")
    
    # 系统统计
    stats = recognizer.get_system_stats()
    print(f"\n🔧 系统统计")
    print("-" * 30)
    print(f"注册猫咪数: {stats['registered_cats']}")
    print(f"当前阈值: {stats['current_threshold']:.3f}")
    
    return {
        'accuracy': accuracy,
        'avg_confidence': avg_confidence,
        'avg_similarity': avg_similarity,
        'avg_response_time': avg_response_time,
        'correct': correct,
        'total': total,
        'stats': stats
    }

def monitor_training_and_test(dataset_path: str, model_path: str):
    """监控训练并定期测试"""
    print("🔄 训练监控模式")
    print("=" * 50)
    
    test_count = 0
    
    while True:
        # 检查模型文件是否存在
        if os.path.exists(model_path):
            test_count += 1
            print(f"\n🧪 第 {test_count} 次测试 (发现模型文件)")
            
            try:
                result = quick_test(dataset_path, model_path, num_cats=3)
                if result:
                    print(f"✅ 测试完成: 准确率 {result['accuracy']:.1%}")
                else:
                    print("❌ 测试失败")
            except Exception as e:
                print(f"❌ 测试异常: {e}")
            
            # 如果准确率达到目标，可以停止
            if result and result['accuracy'] >= 0.9:
                print("🎉 达到目标准确率!")
                break
        
        # 等待一段时间再检查
        time.sleep(30)
        print("⏳ 等待训练进度...")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='快速测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, default=None,
                       help='模型路径')
    parser.add_argument('--cats', type=int, default=3,
                       help='测试猫咪数量')
    parser.add_argument('--monitor', action='store_true',
                       help='监控训练模式')
    
    args = parser.parse_args()
    
    if args.monitor and args.model:
        monitor_training_and_test(args.dataset, args.model)
    else:
        quick_test(args.dataset, args.model, args.cats)

if __name__ == "__main__":
    main()
