#!/usr/bin/env python3
"""
增强识别器 - 使用特征增强训练后的模型
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
from collections import defaultdict
from PIL import Image
import torchvision.transforms as transforms
import timm

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedMegaDescriptor(nn.Module):
    """增强版MegaDescriptor - 与训练脚本保持一致"""
    
    def __init__(self, feature_dim=512):
        super().__init__()
        
        # 加载预训练的MegaDescriptor
        self.backbone = timm.create_model(
            'hf-hub:BVRA/MegaDescriptor-T-224',
            pretrained=True,
            num_classes=0  # 移除分类头
        )
        
        # 获取骨干网络输出维度
        with torch.no_grad():
            dummy_input = torch.randn(1, 3, 224, 224)
            backbone_output = self.backbone(dummy_input)
            backbone_dim = backbone_output.shape[1]
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(backbone_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim, feature_dim)
        )
        
        # L2归一化层
        self.l2_norm = nn.functional.normalize
    
    def forward(self, x):
        # 骨干网络特征提取
        backbone_features = self.backbone(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(backbone_features)
        
        # L2归一化
        normalized_features = self.l2_norm(enhanced_features, p=2, dim=1)
        
        return normalized_features

class EnhancedFeatureExtractor:
    """增强特征提取器"""
    
    def __init__(self, model_path: str = None, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.model = self._load_model(model_path)
        
    def _load_model(self, model_path: str = None):
        """加载模型"""
        try:
            # 创建模型
            model = EnhancedMegaDescriptor(feature_dim=512).to(self.device)
            
            if model_path and os.path.exists(model_path):
                # 加载训练好的权重
                checkpoint = torch.load(model_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                logger.info(f"成功加载增强模型: {model_path}")
            else:
                logger.info("使用预训练的MegaDescriptor模型")
            
            model.eval()
            return model
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            # 备用方案：使用原始MegaDescriptor
            model = timm.create_model(
                'hf-hub:BVRA/MegaDescriptor-T-224',
                pretrained=True,
                num_classes=0
            ).to(self.device)
            model.eval()
            logger.warning("使用备用MegaDescriptor模型")
            return model
    
    def extract_features(self, image_tensor: torch.Tensor) -> np.ndarray:
        """提取特征"""
        try:
            with torch.no_grad():
                if image_tensor.dim() == 3:
                    image_tensor = image_tensor.unsqueeze(0)
                
                image_tensor = image_tensor.to(self.device)
                features = self.model(image_tensor)
                
                # 确保特征是2D的
                if features.dim() > 2:
                    features = features.view(features.size(0), -1)
                
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            # 返回随机特征作为备用
            return np.random.randn(512).astype(np.float32)

class ImprovedSimilarityMatcher:
    """改进的相似度匹配器"""
    
    def __init__(self, similarity_threshold=0.65):
        self.similarity_threshold = similarity_threshold
        self.feature_database = {}
        self.performance_tracker = defaultdict(float)
        
    def add_cat_features(self, cat_id: str, features: np.ndarray):
        """添加猫咪特征到数据库"""
        if cat_id not in self.feature_database:
            self.feature_database[cat_id] = []
        
        # 特征已经在模型中归一化了，直接使用
        self.feature_database[cat_id].append(features)
        
        # 保持合理的特征数量
        if len(self.feature_database[cat_id]) > 15:
            self.feature_database[cat_id] = self.feature_database[cat_id][-15:]
    
    def compute_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算相似度 - 使用余弦相似度"""
        if cat_id not in self.feature_database:
            return 0.0
        
        cat_features_list = self.feature_database[cat_id]
        
        # 计算与所有特征的余弦相似度
        similarities = []
        for cat_features in cat_features_list:
            # 余弦相似度 (特征已归一化，直接点积)
            sim = np.dot(query_features, cat_features)
            similarities.append(sim)
        
        if not similarities:
            return 0.0
        
        similarities = np.array(similarities)
        
        # 使用最高相似度和平均相似度的加权组合
        max_sim = np.max(similarities)
        avg_sim = np.mean(similarities)
        
        # 加权组合：70% 最高相似度 + 30% 平均相似度
        final_similarity = 0.7 * max_sim + 0.3 * avg_sim
        
        return float(final_similarity)
    
    def find_best_matches(self, query_features: np.ndarray, top_k: int = 5) -> List[Tuple[str, float]]:
        """找到最佳匹配"""
        if not self.feature_database:
            return []
        
        matches = []
        for cat_id in self.feature_database:
            similarity = self.compute_similarity(query_features, cat_id)
            matches.append((cat_id, similarity))
        
        # 按相似度排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:top_k]
    
    def get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值"""
        # 基础阈值
        base_threshold = self.similarity_threshold
        
        # 根据猫咪数量调整
        if num_cats <= 3:
            return base_threshold + 0.05
        elif num_cats <= 5:
            return base_threshold
        elif num_cats <= 10:
            return base_threshold - 0.05
        elif num_cats <= 20:
            return base_threshold - 0.1
        else:
            return max(0.45, base_threshold - 0.15)

class EnhancedCatRecognizer:
    """增强猫咪识别器"""
    
    def __init__(self, model_path: str = None, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 核心组件
        self.feature_extractor = EnhancedFeatureExtractor(model_path, device)
        self.similarity_matcher = ImprovedSimilarityMatcher()
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 性能统计
        self.stats = {
            'total_registrations': 0,
            'total_recognitions': 0,
            'correct_recognitions': 0,
            'avg_confidence': 0.0
        }
        
        logger.info("增强猫咪识别系统初始化完成")
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """注册猫咪"""
        try:
            if len(image_paths) < 3:
                return {
                    'success': False,
                    'error': f'图片数量不足，需要至少3张，提供了{len(image_paths)}张'
                }
            
            # 提取特征
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    # 加载和预处理图像
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.transform(image)
                    
                    # 提取特征
                    features = self.feature_extractor.extract_features(image_tensor)
                    
                    # 添加到相似度匹配器
                    self.similarity_matcher.add_cat_features(cat_id, features)
                    valid_images += 1
                        
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            if valid_images < 3:
                return {
                    'success': False,
                    'error': f'有效图片不足，需要至少3张，实际{valid_images}张'
                }
            
            # 保存到数据库
            self.cat_database[cat_id] = {
                'name': cat_name,
                'image_count': valid_images,
                'registration_time': time.time()
            }
            
            self.stats['total_registrations'] += 1
            
            logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), 使用{valid_images}张图片")
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'images_used': valid_images
            }
            
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str) -> Dict:
        """识别猫咪"""
        try:
            # 加载和预处理图像
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image)
            
            # 提取特征
            query_features = self.feature_extractor.extract_features(image_tensor)
            
            # 找到最佳匹配
            matches = self.similarity_matcher.find_best_matches(query_features, top_k=5)
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            best_match_id, best_similarity = matches[0]
            
            # 获取自适应阈值
            num_cats = len(self.cat_database)
            adaptive_threshold = self.similarity_matcher.get_adaptive_threshold(num_cats)
            
            self.stats['total_recognitions'] += 1
            
            if best_similarity >= adaptive_threshold:
                cat_info = self.cat_database[best_match_id]
                confidence = min(0.99, best_similarity * 1.05)  # 置信度调整
                
                # 更新统计
                self.stats['correct_recognitions'] += 1
                self.stats['avg_confidence'] = (
                    self.stats['avg_confidence'] * 0.9 + confidence * 0.1
                )
                
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'cat_name': cat_info['name'],
                    'confidence': confidence,
                    'similarity': best_similarity,
                    'threshold_used': adaptive_threshold,
                    'status': 'recognized',
                    'top_matches': [
                        {
                            'cat_id': cat_id,
                            'cat_name': self.cat_database[cat_id]['name'],
                            'similarity': similarity
                        }
                        for cat_id, similarity in matches[:3]
                    ]
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'threshold_used': adaptive_threshold,
                    'best_match': {
                        'cat_id': best_match_id,
                        'cat_name': self.cat_database[best_match_id]['name'],
                        'similarity': best_similarity
                    }
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        accuracy = (self.stats['correct_recognitions'] / 
                   max(self.stats['total_recognitions'], 1))
        
        return {
            'registered_cats': len(self.cat_database),
            'total_registrations': self.stats['total_registrations'],
            'total_recognitions': self.stats['total_recognitions'],
            'accuracy': accuracy,
            'avg_confidence': self.stats['avg_confidence'],
            'current_threshold': self.similarity_matcher.get_adaptive_threshold(len(self.cat_database))
        }

def create_enhanced_recognizer(model_path: str = None, device='cuda'):
    """创建增强识别器的工厂函数"""
    return EnhancedCatRecognizer(model_path, device)
