#!/usr/bin/env python3
"""
随机抽取测试 - 确保测试的随机性和公正性
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json
from datetime import datetime

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from adaptive_recognizer import create_adaptive_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RandomSamplingTester:
    """随机抽取测试器"""
    
    def __init__(self, dataset_path: str, model_path: str = None):
        self.dataset_path = Path(dataset_path)
        self.model_path = model_path
        
        # 加载所有可用猫咪数据
        self.all_cats = self._load_all_cats()
        
        # 设置随机种子以确保可重现性
        self.random_seed = int(time.time())
        random.seed(self.random_seed)
        np.random.seed(self.random_seed)
        
        logger.info(f"随机抽取测试器初始化完成")
        logger.info(f"可用猫咪总数: {len(self.all_cats)}")
        logger.info(f"随机种子: {self.random_seed}")
    
    def _load_all_cats(self) -> List[Tuple[str, List[str]]]:
        """加载所有猫咪数据"""
        cats = []
        
        for cat_folder in self.dataset_path.iterdir():
            if not cat_folder.is_dir() or not cat_folder.name.isdigit():
                continue
            
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:  # 至少5张图片
                cats.append((cat_folder.name, [str(img) for img in images]))
        
        # 按猫咪ID排序以确保一致性
        cats.sort(key=lambda x: x[0])
        
        return cats
    
    def random_sample_cats(self, num_cats: int, min_images: int = 5) -> List[Tuple[str, List[str]]]:
        """随机抽取指定数量的猫咪"""
        # 过滤出图片数量足够的猫咪
        eligible_cats = [cat for cat in self.all_cats if len(cat[1]) >= min_images]
        
        if len(eligible_cats) < num_cats:
            raise ValueError(f"可用猫咪数量不足: 需要{num_cats}只，实际{len(eligible_cats)}只")
        
        # 随机抽取
        selected_cats = random.sample(eligible_cats, num_cats)
        
        logger.info(f"随机抽取了 {num_cats} 只猫咪:")
        for cat_id, images in selected_cats:
            logger.info(f"  猫咪 {cat_id}: {len(images)} 张图片")
        
        return selected_cats
    
    def run_random_test(self, num_cats: int, strategy: str = 'balanced', 
                       train_ratio: float = 0.7, rounds: int = 5) -> Dict:
        """运行随机抽取测试"""
        print(f"🎲 随机抽取测试")
        print("=" * 60)
        print(f"测试规模: {num_cats} 只猫咪")
        print(f"策略: {strategy}")
        print(f"训练比例: {train_ratio:.1%}")
        print(f"测试轮数: {rounds}")
        print(f"随机种子: {self.random_seed}")
        
        all_results = []
        
        for round_num in range(rounds):
            print(f"\n🔄 第 {round_num + 1} 轮测试")
            print("-" * 40)
            
            # 每轮重新随机抽取猫咪
            try:
                selected_cats = self.random_sample_cats(num_cats)
            except ValueError as e:
                print(f"❌ 抽取失败: {e}")
                continue
            
            # 创建识别器
            recognizer = create_adaptive_recognizer(self.model_path, device='cpu', strategy=strategy)
            
            # 准备训练和测试数据
            train_data = []
            test_data = []
            
            for cat_id, image_paths in selected_cats:
                # 随机打乱图片顺序
                shuffled_images = image_paths.copy()
                random.shuffle(shuffled_images)
                
                # 分割训练和测试数据
                train_count = max(3, int(len(shuffled_images) * train_ratio))
                train_images = shuffled_images[:train_count]
                test_images = shuffled_images[train_count:]
                
                if test_images:  # 确保有测试图片
                    train_data.append((cat_id, train_images))
                    test_data.append((cat_id, random.choice(test_images)))
            
            # 注册阶段
            print("📝 注册阶段:")
            registration_success = 0
            
            for cat_id, train_images in train_data:
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success']:
                    registration_success += 1
                    print(f"  ✅ {cat_id}: {result['images_used']} 张图片")
                else:
                    print(f"  ❌ {cat_id}: {result.get('error', '注册失败')}")
            
            print(f"注册成功率: {registration_success}/{len(train_data)} ({registration_success/len(train_data):.1%})")
            
            # 识别阶段
            print("\n🔍 识别阶段:")
            correct = 0
            total = 0
            confidences = []
            similarities = []
            response_times = []
            detailed_results = []
            
            for cat_id, test_image in test_data:
                start_time = time.time()
                result = recognizer.recognize_cat(test_image)
                response_time = time.time() - start_time
                
                response_times.append(response_time)
                total += 1
                
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                predicted_id = result.get('cat_id', 'unknown')
                confidence = result.get('confidence', 0.0)
                similarity = result.get('similarity', 0.0)
                threshold = result.get('threshold_used', 0.0)
                
                if is_correct:
                    correct += 1
                    confidences.append(confidence)
                
                similarities.append(similarity)
                
                # 记录详细结果
                detailed_results.append({
                    'true_id': cat_id,
                    'predicted_id': predicted_id,
                    'correct': is_correct,
                    'confidence': confidence,
                    'similarity': similarity,
                    'threshold': threshold,
                    'response_time': response_time
                })
                
                # 显示结果
                status = "✅" if is_correct else "❌"
                print(f"  {status} 真实:{cat_id} 预测:{predicted_id} "
                      f"置信度:{confidence:.1%} 相似度:{similarity:.3f} ({response_time:.3f}s)")
            
            # 计算轮次统计
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            avg_response_time = np.mean(response_times) if response_times else 0.0
            
            round_result = {
                'round': round_num + 1,
                'accuracy': accuracy,
                'correct': correct,
                'total': total,
                'avg_confidence': avg_confidence,
                'avg_similarity': avg_similarity,
                'avg_response_time': avg_response_time,
                'registration_success_rate': registration_success / len(train_data),
                'selected_cats': [cat_id for cat_id, _ in selected_cats],
                'detailed_results': detailed_results
            }
            
            all_results.append(round_result)
            
            print(f"\n📊 轮次结果:")
            print(f"  准确率: {accuracy:.1%} ({correct}/{total})")
            print(f"  平均置信度: {avg_confidence:.1%}")
            print(f"  平均相似度: {avg_similarity:.3f}")
            print(f"  平均响应时间: {avg_response_time:.3f}s")
        
        return self._calculate_overall_stats(all_results, num_cats, strategy)
    
    def _calculate_overall_stats(self, all_results: List[Dict], num_cats: int, strategy: str) -> Dict:
        """计算总体统计"""
        if not all_results:
            return {}
        
        accuracies = [r['accuracy'] for r in all_results]
        confidences = [r['avg_confidence'] for r in all_results]
        similarities = [r['avg_similarity'] for r in all_results]
        response_times = [r['avg_response_time'] for r in all_results]
        
        overall_stats = {
            'test_info': {
                'num_cats': num_cats,
                'strategy': strategy,
                'rounds': len(all_results),
                'random_seed': self.random_seed,
                'test_time': datetime.now().isoformat()
            },
            'performance': {
                'avg_accuracy': np.mean(accuracies),
                'accuracy_std': np.std(accuracies),
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'avg_confidence': np.mean(confidences),
                'avg_similarity': np.mean(similarities),
                'avg_response_time': np.mean(response_times)
            },
            'stability': {
                'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
                'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95),
                'good_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.9),
                'consistency_score': 1.0 - np.std(accuracies)  # 一致性评分
            },
            'detailed_rounds': all_results
        }
        
        return overall_stats
    
    def run_multi_scale_random_test(self, scales: List[int], strategy: str = 'balanced', 
                                   rounds_per_scale: int = 5) -> Dict:
        """运行多规模随机测试"""
        print("🎲 多规模随机抽取测试")
        print("=" * 80)
        print(f"测试规模: {scales}")
        print(f"策略: {strategy}")
        print(f"每个规模轮数: {rounds_per_scale}")
        
        all_scale_results = {}
        
        for scale in scales:
            print(f"\n🎯 测试规模: {scale} 只猫咪")
            print("=" * 60)
            
            try:
                scale_result = self.run_random_test(scale, strategy, rounds=rounds_per_scale)
                all_scale_results[scale] = scale_result
                
                # 显示规模总结
                perf = scale_result['performance']
                stab = scale_result['stability']
                
                print(f"\n📈 规模 {scale} 总结:")
                print(f"  平均准确率: {perf['avg_accuracy']:.1%} ± {perf['accuracy_std']:.1%}")
                print(f"  准确率范围: {perf['min_accuracy']:.1%} - {perf['max_accuracy']:.1%}")
                print(f"  高准确率轮次: {stab['high_accuracy_rounds']}/{rounds_per_scale}")
                print(f"  一致性评分: {stab['consistency_score']:.3f}")
                
            except Exception as e:
                print(f"❌ 规模 {scale} 测试失败: {e}")
                continue
        
        return all_scale_results

def display_random_test_summary(results: Dict):
    """显示随机测试总结"""
    print("\n" + "=" * 100)
    print("🎲 随机抽取测试总结")
    print("=" * 100)
    
    if not results:
        print("没有测试结果")
        return
    
    # 总体表现表格
    print(f"{'规模':<6} {'平均准确率':<12} {'准确率范围':<15} {'一致性':<8} {'高准确率轮次':<12} {'评级':<8}")
    print("-" * 80)
    
    overall_best_accuracy = 0
    target_95_achieved = 0
    target_90_achieved = 0
    
    for scale in sorted(results.keys()):
        result = results[scale]
        perf = result['performance']
        stab = result['stability']
        
        avg_acc = perf['avg_accuracy']
        min_acc = perf['min_accuracy']
        max_acc = perf['max_accuracy']
        consistency = stab['consistency_score']
        high_acc_ratio = stab['high_accuracy_rounds'] / len(result['detailed_rounds'])
        
        # 评级
        if avg_acc >= 0.95:
            grade = "A+"
            target_95_achieved += 1
        elif avg_acc >= 0.9:
            grade = "A"
            target_90_achieved += 1
        elif avg_acc >= 0.8:
            grade = "B"
        elif avg_acc >= 0.7:
            grade = "C"
        else:
            grade = "D"
        
        print(f"{scale:<6} {avg_acc:<12.1%} {min_acc:.1%}-{max_acc:.1%}{'':>6} "
              f"{consistency:<8.3f} {high_acc_ratio:<12.1%} {grade:<8}")
        
        if avg_acc > overall_best_accuracy:
            overall_best_accuracy = avg_acc
    
    # 总体评价
    total_scales = len(results)
    print(f"\n🏆 随机测试评价:")
    print(f"   最高准确率: {overall_best_accuracy:.1%}")
    print(f"   达到95%+准确率的规模: {target_95_achieved}/{total_scales} ({target_95_achieved/total_scales:.1%})")
    print(f"   达到90%+准确率的规模: {target_90_achieved}/{total_scales} ({target_90_achieved/total_scales:.1%})")
    
    # 随机性验证
    print(f"\n🔍 随机性验证:")
    for scale in sorted(results.keys()):
        result = results[scale]
        all_cats_used = set()
        for round_result in result['detailed_rounds']:
            all_cats_used.update(round_result['selected_cats'])
        
        coverage = len(all_cats_used) / len(results[scale]['detailed_rounds']) / scale
        print(f"   规模 {scale}: 使用了 {len(all_cats_used)} 只不同猫咪 (覆盖率: {coverage:.1f})")
    
    # 最终评级
    if target_95_achieved == total_scales:
        final_rating = "🌟 完美 - 所有规模都达到95%+目标!"
    elif target_95_achieved >= total_scales * 0.8:
        final_rating = "🎉 优秀 - 大部分规模达到95%+目标"
    elif target_90_achieved == total_scales:
        final_rating = "✅ 良好 - 所有规模都达到90%+目标"
    elif target_90_achieved >= total_scales * 0.8:
        final_rating = "📈 不错 - 大部分规模达到90%+目标"
    else:
        final_rating = "⚠️ 需要改进 - 随机测试表现不稳定"
    
    print(f"\n📊 最终评级: {final_rating}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='随机抽取测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--strategy', type=str, default='balanced',
                       choices=['conservative', 'balanced', 'aggressive'],
                       help='测试策略')
    parser.add_argument('--rounds', type=int, default=5,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='random_test_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = RandomSamplingTester(args.dataset, args.model)
    
    # 运行多规模随机测试
    results = tester.run_multi_scale_random_test(args.scales, args.strategy, args.rounds)
    
    # 显示总结
    display_random_test_summary(results)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 随机测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
