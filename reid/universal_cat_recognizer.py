#!/usr/bin/env python3
"""
通用猫咪识别系统 - 支持任意数量猫咪的高精度识别
核心设计理念：规模无关、自适应、高精度
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
import json
from collections import defaultdict
import cv2
from PIL import Image
import torchvision.transforms as transforms

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalFeatureExtractor(nn.Module):
    """通用特征提取器 - 规模无关的高质量特征"""
    
    def __init__(self, backbone_name='BVRA/MegaDescriptor-L-384', feature_dim=1024):
        super().__init__()
        self.backbone_name = backbone_name
        self.feature_dim = feature_dim
        
        # 加载预训练骨干网络
        self.backbone = self._load_backbone()
        
        # 多层特征融合
        self.feature_fusion = nn.ModuleDict({
            'global_pool': nn.AdaptiveAvgPool2d(1),
            'attention_pool': nn.MultiheadAttention(feature_dim, num_heads=16, batch_first=True),
            'local_pool': nn.AdaptiveMaxPool2d(1)
        })
        
        # 特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.LayerNorm(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.LayerNorm(feature_dim)
        )
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.GELU(),
            nn.Linear(feature_dim // 2, feature_dim // 4)
        )
        
    def _load_backbone(self):
        """加载骨干网络 - 使用专门的动物识别模型"""
        try:
            # 使用专门为动物识别设计的MegaDescriptor模型
            import timm
            model = timm.create_model(
                'hf-hub:BVRA/MegaDescriptor-L-384',
                pretrained=True,
                num_classes=0,  # 移除分类头
                global_pool=''  # 保留空间维度
            )
            logger.info("成功加载MegaDescriptor-L-384动物识别模型")
            return model
        except Exception as e:
            logger.warning(f"无法加载MegaDescriptor模型，尝试备用方案: {e}")
            try:
                # 备用方案：使用MegaDescriptor-T-224
                model = timm.create_model(
                    'hf-hub:BVRA/MegaDescriptor-T-224',
                    pretrained=True,
                    num_classes=0,
                    global_pool=''
                )
                logger.info("成功加载MegaDescriptor-T-224动物识别模型")
                return model
            except Exception as e2:
                logger.error(f"无法加载任何MegaDescriptor模型: {e2}")
                # 最后备用方案：使用ResNet
                import torchvision.models as models
                resnet = models.resnet50(pretrained=True)
                logger.warning("使用ResNet50作为备用骨干网络")
                return nn.Sequential(*list(resnet.children())[:-2])
    
    def forward(self, x):
        """前向传播"""
        # 骨干网络特征提取
        backbone_features = self.backbone(x)  # [B, C, H, W] or [B, N, C]
        
        if len(backbone_features.shape) == 3:  # ViT输出 [B, N, C]
            # 处理ViT特征
            B, N, C = backbone_features.shape
            
            # 全局特征 (CLS token或平均)
            global_feat = backbone_features[:, 0, :] if N > 1 else backbone_features.mean(dim=1)
            
            # 注意力特征
            attn_feat, _ = self.feature_fusion['attention_pool'](
                backbone_features, backbone_features, backbone_features
            )
            attn_feat = attn_feat.mean(dim=1)  # 平均池化
            
            # 局部特征 (最大值)
            local_feat = backbone_features.max(dim=1)[0]
            
        else:  # CNN输出 [B, C, H, W]
            # 处理CNN特征
            global_feat = self.feature_fusion['global_pool'](backbone_features).flatten(1)
            local_feat = self.feature_fusion['local_pool'](backbone_features).flatten(1)
            
            # 注意力特征 (简化处理)
            B, C, H, W = backbone_features.shape
            spatial_feat = backbone_features.view(B, C, H*W).transpose(1, 2)  # [B, HW, C]
            attn_feat, _ = self.feature_fusion['attention_pool'](
                spatial_feat, spatial_feat, spatial_feat
            )
            attn_feat = attn_feat.mean(dim=1)  # [B, C]
        
        # 特征融合
        fused_features = torch.cat([global_feat, attn_feat, local_feat], dim=1)
        enhanced_features = self.feature_enhancer(fused_features)
        
        # 对比学习特征
        contrastive_features = self.projection_head(enhanced_features)
        
        return {
            'enhanced_features': enhanced_features,
            'contrastive_features': contrastive_features,
            'raw_features': backbone_features
        }

class AdaptiveSimilarityMatcher:
    """自适应相似度匹配器 - 规模无关的智能匹配"""
    
    def __init__(self, similarity_threshold=0.75):
        self.similarity_threshold = similarity_threshold
        self.feature_database = {}
        self.similarity_history = defaultdict(list)
        self.performance_tracker = defaultdict(float)
        
    def add_cat_features(self, cat_id: str, features: np.ndarray):
        """添加猫咪特征到数据库"""
        if cat_id not in self.feature_database:
            self.feature_database[cat_id] = []
        
        # 归一化特征
        normalized_features = features / (np.linalg.norm(features) + 1e-8)
        self.feature_database[cat_id].append(normalized_features)
        
        # 保持合理的特征数量（避免内存爆炸）
        if len(self.feature_database[cat_id]) > 20:
            self.feature_database[cat_id] = self.feature_database[cat_id][-20:]
    
    def compute_adaptive_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算自适应相似度"""
        if cat_id not in self.feature_database:
            return 0.0
        
        cat_features_list = self.feature_database[cat_id]
        query_normalized = query_features / (np.linalg.norm(query_features) + 1e-8)
        
        # 计算与所有特征的相似度
        similarities = []
        for cat_features in cat_features_list:
            sim = np.dot(query_normalized, cat_features)
            similarities.append(sim)
        
        # 自适应聚合策略
        similarities = np.array(similarities)
        
        # 使用加权平均，给最近的特征更高权重
        weights = np.exp(similarities - similarities.max())  # softmax权重
        weighted_similarity = np.average(similarities, weights=weights)
        
        # 考虑特征一致性
        consistency_bonus = 1.0 - np.std(similarities) * 0.5
        final_similarity = weighted_similarity * consistency_bonus
        
        return float(final_similarity)
    
    def find_best_matches(self, query_features: np.ndarray, top_k: int = 5) -> List[Tuple[str, float]]:
        """找到最佳匹配"""
        if not self.feature_database:
            return []
        
        matches = []
        for cat_id in self.feature_database:
            similarity = self.compute_adaptive_similarity(query_features, cat_id)
            matches.append((cat_id, similarity))
        
        # 按相似度排序
        matches.sort(key=lambda x: x[1], reverse=True)
        
        return matches[:top_k]
    
    def update_performance(self, cat_id: str, is_correct: bool):
        """更新性能跟踪"""
        alpha = 0.1  # 学习率
        current_performance = self.performance_tracker[cat_id]
        new_performance = current_performance * (1 - alpha) + (1.0 if is_correct else 0.0) * alpha
        self.performance_tracker[cat_id] = new_performance
    
    def get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值"""
        # 基础阈值
        base_threshold = self.similarity_threshold
        
        # 规模调整（轻微）
        scale_adjustment = max(0.0, min(0.1, (num_cats - 10) * 0.005))
        
        # 性能调整
        if self.performance_tracker:
            avg_performance = np.mean(list(self.performance_tracker.values()))
            performance_adjustment = (avg_performance - 0.8) * 0.1
        else:
            performance_adjustment = 0.0
        
        adaptive_threshold = base_threshold - scale_adjustment + performance_adjustment
        return max(0.5, min(0.95, adaptive_threshold))

class UniversalCatRecognizer:
    """通用猫咪识别器主类"""
    
    def __init__(self, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 核心组件
        self.feature_extractor = UniversalFeatureExtractor().to(self.device)
        self.similarity_matcher = AdaptiveSimilarityMatcher()
        
        # 图像预处理 - 使用384x384以匹配MegaDescriptor-L-384
        self.transform = transforms.Compose([
            transforms.Resize((384, 384)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 性能统计
        self.stats = {
            'total_registrations': 0,
            'total_recognitions': 0,
            'correct_recognitions': 0,
            'avg_confidence': 0.0
        }
        
        logger.info("通用猫咪识别系统初始化完成")
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """注册猫咪"""
        try:
            if len(image_paths) < 3:
                return {
                    'success': False,
                    'error': f'图片数量不足，需要至少3张，提供了{len(image_paths)}张'
                }
            
            # 提取特征
            features_list = []
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    # 加载和预处理图像
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.transform(image).unsqueeze(0).to(self.device)
                    
                    # 提取特征
                    with torch.no_grad():
                        feature_dict = self.feature_extractor(image_tensor)
                        features = feature_dict['enhanced_features'].cpu().numpy().flatten()
                        features_list.append(features)
                        valid_images += 1
                        
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            if valid_images < 3:
                return {
                    'success': False,
                    'error': f'有效图片不足，需要至少3张，实际{valid_images}张'
                }
            
            # 添加到相似度匹配器
            for features in features_list:
                self.similarity_matcher.add_cat_features(cat_id, features)
            
            # 保存到数据库
            self.cat_database[cat_id] = {
                'name': cat_name,
                'image_count': valid_images,
                'registration_time': time.time()
            }
            
            self.stats['total_registrations'] += 1
            
            logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), 使用{valid_images}张图片")
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'images_used': valid_images
            }
            
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str) -> Dict:
        """识别猫咪"""
        try:
            # 加载和预处理图像
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            # 提取特征
            with torch.no_grad():
                feature_dict = self.feature_extractor(image_tensor)
                query_features = feature_dict['enhanced_features'].cpu().numpy().flatten()
            
            # 找到最佳匹配
            matches = self.similarity_matcher.find_best_matches(query_features, top_k=5)
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            best_match_id, best_similarity = matches[0]
            
            # 获取自适应阈值
            num_cats = len(self.cat_database)
            adaptive_threshold = self.similarity_matcher.get_adaptive_threshold(num_cats)
            
            self.stats['total_recognitions'] += 1
            
            if best_similarity >= adaptive_threshold:
                cat_info = self.cat_database[best_match_id]
                confidence = min(0.99, best_similarity * 1.05)  # 轻微提升置信度显示
                
                # 更新统计
                self.stats['correct_recognitions'] += 1
                self.stats['avg_confidence'] = (
                    self.stats['avg_confidence'] * 0.9 + confidence * 0.1
                )
                
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'cat_name': cat_info['name'],
                    'confidence': confidence,
                    'similarity': best_similarity,
                    'threshold_used': adaptive_threshold,
                    'status': 'recognized',
                    'top_matches': [
                        {
                            'cat_id': cat_id,
                            'cat_name': self.cat_database[cat_id]['name'],
                            'similarity': similarity
                        }
                        for cat_id, similarity in matches[:3]
                    ]
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'threshold_used': adaptive_threshold,
                    'best_match': {
                        'cat_id': best_match_id,
                        'cat_name': self.cat_database[best_match_id]['name'],
                        'similarity': best_similarity
                    }
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        accuracy = (self.stats['correct_recognitions'] / 
                   max(self.stats['total_recognitions'], 1))
        
        return {
            'registered_cats': len(self.cat_database),
            'total_registrations': self.stats['total_registrations'],
            'total_recognitions': self.stats['total_recognitions'],
            'accuracy': accuracy,
            'avg_confidence': self.stats['avg_confidence'],
            'current_threshold': self.similarity_matcher.get_adaptive_threshold(len(self.cat_database))
        }

def create_universal_recognizer(device='cuda'):
    """创建通用识别器的工厂函数"""
    return UniversalCatRecognizer(device)
