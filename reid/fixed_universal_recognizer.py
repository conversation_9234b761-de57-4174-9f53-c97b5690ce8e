#!/usr/bin/env python3
"""
修正版通用猫咪识别系统 - 使用正确的MegaDescriptor模型
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
from collections import defaultdict
from PIL import Image
import torchvision.transforms as transforms

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.join(current_dir, 'training'))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FixedMegaDescriptorExtractor:
    """修正版MegaDescriptor特征提取器"""
    
    def __init__(self, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.model = self._load_model()
        
    def _load_model(self):
        """加载MegaDescriptor模型"""
        try:
            import timm
            # 使用MegaDescriptor-T-224，这个模型更稳定
            model = timm.create_model(
                'hf-hub:BVRA/MegaDescriptor-T-224',
                pretrained=True,
                num_classes=0  # 移除分类头，只要特征
            )
            model = model.to(self.device)
            model.eval()
            logger.info("成功加载MegaDescriptor-T-224模型")
            return model
        except Exception as e:
            logger.error(f"加载MegaDescriptor模型失败: {e}")
            # 备用方案：使用ResNet50
            import torchvision.models as models
            model = models.resnet50(pretrained=True)
            model.fc = nn.Identity()  # 移除分类层
            model = model.to(self.device)
            model.eval()
            logger.warning("使用ResNet50作为备用模型")
            return model
    
    def extract_features(self, image_tensor: torch.Tensor) -> np.ndarray:
        """提取特征"""
        try:
            with torch.no_grad():
                if image_tensor.dim() == 3:
                    image_tensor = image_tensor.unsqueeze(0)
                
                image_tensor = image_tensor.to(self.device)
                features = self.model(image_tensor)
                
                # 确保特征是2D的
                if features.dim() > 2:
                    features = features.view(features.size(0), -1)
                
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            # 返回随机特征作为备用
            return np.random.randn(2048).astype(np.float32)

class AdaptiveSimilarityMatcher:
    """自适应相似度匹配器"""
    
    def __init__(self, similarity_threshold=0.75):
        self.similarity_threshold = similarity_threshold
        self.feature_database = {}
        self.performance_tracker = defaultdict(float)
        
    def add_cat_features(self, cat_id: str, features: np.ndarray):
        """添加猫咪特征到数据库"""
        if cat_id not in self.feature_database:
            self.feature_database[cat_id] = []
        
        # 归一化特征
        normalized_features = features / (np.linalg.norm(features) + 1e-8)
        self.feature_database[cat_id].append(normalized_features)
        
        # 保持合理的特征数量
        if len(self.feature_database[cat_id]) > 10:
            self.feature_database[cat_id] = self.feature_database[cat_id][-10:]
    
    def compute_similarity(self, query_features: np.ndarray, cat_id: str) -> float:
        """计算相似度 - 改进的聚合策略"""
        if cat_id not in self.feature_database:
            return 0.0

        cat_features_list = self.feature_database[cat_id]
        query_normalized = query_features / (np.linalg.norm(query_features) + 1e-8)

        # 计算与所有特征的相似度
        similarities = []
        for cat_features in cat_features_list:
            sim = np.dot(query_normalized, cat_features)
            similarities.append(sim)

        if not similarities:
            return 0.0

        similarities = np.array(similarities)

        # 使用加权平均：最高相似度权重更大
        sorted_sims = np.sort(similarities)[::-1]  # 降序排列

        # 给前几个最高相似度更大权重
        if len(sorted_sims) >= 3:
            # 前3个的加权平均
            weights = np.array([0.5, 0.3, 0.2])
            weighted_sim = np.average(sorted_sims[:3], weights=weights)
        elif len(sorted_sims) == 2:
            # 前2个的加权平均
            weights = np.array([0.7, 0.3])
            weighted_sim = np.average(sorted_sims[:2], weights=weights)
        else:
            # 只有1个
            weighted_sim = sorted_sims[0]

        return float(weighted_sim)
    
    def find_best_matches(self, query_features: np.ndarray, top_k: int = 5) -> List[Tuple[str, float]]:
        """找到最佳匹配"""
        if not self.feature_database:
            return []
        
        matches = []
        for cat_id in self.feature_database:
            similarity = self.compute_similarity(query_features, cat_id)
            matches.append((cat_id, similarity))
        
        # 按相似度排序
        matches.sort(key=lambda x: x[1], reverse=True)
        return matches[:top_k]
    
    def get_adaptive_threshold(self, num_cats: int) -> float:
        """获取自适应阈值 - 更宽松的阈值设置"""
        # 基础阈值降低
        base_threshold = 0.6  # 从0.75降低到0.6

        # 根据猫咪数量调整 - 更宽松的调整
        if num_cats <= 3:
            return base_threshold + 0.1  # 小规模时稍微严格一些
        elif num_cats <= 5:
            return base_threshold
        elif num_cats <= 10:
            return base_threshold - 0.05
        elif num_cats <= 20:
            return base_threshold - 0.1
        else:
            return max(0.4, base_threshold - 0.15)  # 最低阈值从0.5降到0.4

class FixedUniversalCatRecognizer:
    """修正版通用猫咪识别器"""
    
    def __init__(self, device='cuda'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        
        # 核心组件
        self.feature_extractor = FixedMegaDescriptorExtractor(device)
        self.similarity_matcher = AdaptiveSimilarityMatcher()
        
        # 图像预处理 - 使用224x224以匹配MegaDescriptor-T-224
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], 
                               std=[0.229, 0.224, 0.225])
        ])
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 性能统计
        self.stats = {
            'total_registrations': 0,
            'total_recognitions': 0,
            'correct_recognitions': 0,
            'avg_confidence': 0.0
        }
        
        logger.info("修正版通用猫咪识别系统初始化完成")
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """注册猫咪"""
        try:
            if len(image_paths) < 3:
                return {
                    'success': False,
                    'error': f'图片数量不足，需要至少3张，提供了{len(image_paths)}张'
                }
            
            # 提取特征
            valid_images = 0
            
            for img_path in image_paths:
                try:
                    # 加载和预处理图像
                    image = Image.open(img_path).convert('RGB')
                    image_tensor = self.transform(image)
                    
                    # 提取特征
                    features = self.feature_extractor.extract_features(image_tensor)
                    
                    # 添加到相似度匹配器
                    self.similarity_matcher.add_cat_features(cat_id, features)
                    valid_images += 1
                        
                except Exception as e:
                    logger.warning(f"处理图片失败 {img_path}: {e}")
                    continue
            
            if valid_images < 3:
                return {
                    'success': False,
                    'error': f'有效图片不足，需要至少3张，实际{valid_images}张'
                }
            
            # 保存到数据库
            self.cat_database[cat_id] = {
                'name': cat_name,
                'image_count': valid_images,
                'registration_time': time.time()
            }
            
            self.stats['total_registrations'] += 1
            
            logger.info(f"成功注册猫咪: {cat_name} (ID: {cat_id}), 使用{valid_images}张图片")
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'images_used': valid_images
            }
            
        except Exception as e:
            logger.error(f"注册猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def recognize_cat(self, image_path: str) -> Dict:
        """识别猫咪"""
        try:
            # 加载和预处理图像
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image)
            
            # 提取特征
            query_features = self.feature_extractor.extract_features(image_tensor)
            
            # 找到最佳匹配
            matches = self.similarity_matcher.find_best_matches(query_features, top_k=5)
            
            if not matches:
                return {
                    'success': False,
                    'message': '没有注册的猫咪',
                    'status': 'no_cats'
                }
            
            best_match_id, best_similarity = matches[0]
            
            # 获取自适应阈值
            num_cats = len(self.cat_database)
            adaptive_threshold = self.similarity_matcher.get_adaptive_threshold(num_cats)
            
            self.stats['total_recognitions'] += 1
            
            if best_similarity >= adaptive_threshold:
                cat_info = self.cat_database[best_match_id]
                confidence = min(0.99, best_similarity * 1.1)  # 置信度调整
                
                # 更新统计
                self.stats['correct_recognitions'] += 1
                self.stats['avg_confidence'] = (
                    self.stats['avg_confidence'] * 0.9 + confidence * 0.1
                )
                
                return {
                    'success': True,
                    'cat_id': best_match_id,
                    'cat_name': cat_info['name'],
                    'confidence': confidence,
                    'similarity': best_similarity,
                    'threshold_used': adaptive_threshold,
                    'status': 'recognized',
                    'top_matches': [
                        {
                            'cat_id': cat_id,
                            'cat_name': self.cat_database[cat_id]['name'],
                            'similarity': similarity
                        }
                        for cat_id, similarity in matches[:3]
                    ]
                }
            else:
                return {
                    'success': False,
                    'message': f'相似度过低 ({best_similarity:.3f} < {adaptive_threshold:.3f})',
                    'status': 'unknown',
                    'threshold_used': adaptive_threshold,
                    'best_match': {
                        'cat_id': best_match_id,
                        'cat_name': self.cat_database[best_match_id]['name'],
                        'similarity': best_similarity
                    }
                }
                
        except Exception as e:
            logger.error(f"识别猫咪失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_system_stats(self) -> Dict:
        """获取系统统计信息"""
        accuracy = (self.stats['correct_recognitions'] / 
                   max(self.stats['total_recognitions'], 1))
        
        return {
            'registered_cats': len(self.cat_database),
            'total_registrations': self.stats['total_registrations'],
            'total_recognitions': self.stats['total_recognitions'],
            'accuracy': accuracy,
            'avg_confidence': self.stats['avg_confidence'],
            'current_threshold': self.similarity_matcher.get_adaptive_threshold(len(self.cat_database))
        }

def create_fixed_universal_recognizer(device='cuda'):
    """创建修正版通用识别器的工厂函数"""
    return FixedUniversalCatRecognizer(device)
