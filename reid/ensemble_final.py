#!/usr/bin/env python3
"""
最终集成学习实现 - 结合多种策略的终极解决方案
"""

import os
import sys
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
import time
from collections import defaultdict
import random

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from adaptive_recognizer import create_adaptive_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnsembleFinalRecognizer:
    """最终集成识别器 - 结合多种策略和投票机制"""
    
    def __init__(self, model_path: str = None, device='cpu'):
        self.device = device
        self.model_path = model_path
        
        # 创建多个不同策略的识别器
        self.recognizers = {
            'conservative': create_adaptive_recognizer(model_path, device, 'conservative'),
            'balanced': create_adaptive_recognizer(model_path, device, 'balanced'),
            'aggressive': create_adaptive_recognizer(model_path, device, 'aggressive')
        }
        
        # 策略权重 - 可以根据历史表现调整
        self.strategy_weights = {
            'conservative': 1.0,
            'balanced': 1.0,
            'aggressive': 1.0
        }
        
        # 猫咪数据库
        self.cat_database = {}
        
        # 性能统计
        self.stats = {
            'total_registrations': 0,
            'total_recognitions': 0,
            'correct_recognitions': 0,
            'strategy_performance': defaultdict(list)
        }
        
        logger.info("最终集成识别器初始化完成")
    
    def register_cat(self, cat_id: str, cat_name: str, image_paths: List[str]) -> Dict:
        """在所有策略中注册猫咪"""
        results = {}
        
        for strategy, recognizer in self.recognizers.items():
            try:
                result = recognizer.register_cat(cat_id, cat_name, image_paths)
                results[strategy] = result
            except Exception as e:
                logger.error(f"策略 {strategy} 注册失败: {e}")
                results[strategy] = {'success': False, 'error': str(e)}
        
        # 统计成功率
        successful = sum(1 for r in results.values() if r.get('success', False))
        
        if successful > 0:
            # 保存到数据库
            self.cat_database[cat_id] = {
                'name': cat_name,
                'registration_time': time.time(),
                'successful_strategies': successful
            }
            
            self.stats['total_registrations'] += 1
            
            return {
                'success': True,
                'cat_id': cat_id,
                'cat_name': cat_name,
                'successful_strategies': successful,
                'total_strategies': len(self.recognizers),
                'strategy_results': results
            }
        else:
            return {
                'success': False,
                'error': '所有策略注册失败',
                'strategy_results': results
            }
    
    def recognize_cat(self, image_path: str) -> Dict:
        """集成识别"""
        start_time = time.time()
        
        # 收集所有策略的结果
        strategy_results = {}
        
        for strategy, recognizer in self.recognizers.items():
            try:
                result = recognizer.recognize_cat(image_path)
                strategy_results[strategy] = result
            except Exception as e:
                logger.error(f"策略 {strategy} 识别失败: {e}")
                strategy_results[strategy] = {'success': False, 'error': str(e)}
        
        # 集成决策
        ensemble_result = self._ensemble_decision(strategy_results)
        ensemble_result['response_time'] = time.time() - start_time
        
        # 更新统计
        self.stats['total_recognitions'] += 1
        if ensemble_result.get('success'):
            self.stats['correct_recognitions'] += 1
        
        return ensemble_result
    
    def _ensemble_decision(self, strategy_results: Dict) -> Dict:
        """集成决策算法"""
        # 收集成功的预测
        successful_predictions = {}
        
        for strategy, result in strategy_results.items():
            if result.get('success'):
                cat_id = result.get('cat_id')
                confidence = result.get('confidence', 0.0)
                similarity = result.get('similarity', 0.0)
                
                if cat_id:
                    if cat_id not in successful_predictions:
                        successful_predictions[cat_id] = {
                            'votes': 0,
                            'weighted_votes': 0.0,
                            'confidences': [],
                            'similarities': [],
                            'strategies': []
                        }
                    
                    weight = self.strategy_weights[strategy]
                    successful_predictions[cat_id]['votes'] += 1
                    successful_predictions[cat_id]['weighted_votes'] += weight
                    successful_predictions[cat_id]['confidences'].append(confidence)
                    successful_predictions[cat_id]['similarities'].append(similarity)
                    successful_predictions[cat_id]['strategies'].append(strategy)
        
        if not successful_predictions:
            return {
                'success': False,
                'status': 'no_consensus',
                'error': '没有策略达成一致',
                'strategy_results': strategy_results
            }
        
        # 找到得票最高的猫咪
        best_cat_id = max(successful_predictions.keys(), 
                         key=lambda x: successful_predictions[x]['weighted_votes'])
        
        prediction = successful_predictions[best_cat_id]
        
        # 计算集成置信度和相似度
        ensemble_confidence = np.mean(prediction['confidences'])
        ensemble_similarity = np.mean(prediction['similarities'])
        
        # 决策阈值
        min_votes = 1  # 至少需要1票
        min_weighted_votes = 0.5  # 加权投票阈值
        
        if (prediction['votes'] >= min_votes and 
            prediction['weighted_votes'] >= min_weighted_votes):
            
            cat_info = self.cat_database.get(best_cat_id, {'name': f'Cat_{best_cat_id}'})
            
            return {
                'success': True,
                'cat_id': best_cat_id,
                'cat_name': cat_info['name'],
                'confidence': ensemble_confidence,
                'similarity': ensemble_similarity,
                'votes': prediction['votes'],
                'weighted_votes': prediction['weighted_votes'],
                'supporting_strategies': prediction['strategies'],
                'status': 'recognized',
                'ensemble_info': {
                    'total_strategies': len(self.recognizers),
                    'successful_strategies': len([r for r in strategy_results.values() if r.get('success')]),
                    'consensus_strength': prediction['weighted_votes'] / len(self.recognizers)
                }
            }
        else:
            return {
                'success': False,
                'status': 'low_consensus',
                'best_candidate': {
                    'cat_id': best_cat_id,
                    'cat_name': self.cat_database.get(best_cat_id, {'name': f'Cat_{best_cat_id}'})['name'],
                    'votes': prediction['votes'],
                    'weighted_votes': prediction['weighted_votes'],
                    'confidence': ensemble_confidence,
                    'similarity': ensemble_similarity
                },
                'error': f'投票不足 (票数:{prediction["votes"]}, 加权:{prediction["weighted_votes"]:.2f})'
            }
    
    def update_strategy_weights(self, performance_data: Dict):
        """根据性能数据更新策略权重"""
        total_performance = sum(performance_data.values())
        
        if total_performance > 0:
            for strategy in self.strategy_weights:
                if strategy in performance_data:
                    # 基于相对性能调整权重
                    relative_performance = performance_data[strategy] / total_performance
                    self.strategy_weights[strategy] = relative_performance * len(performance_data)
        
        logger.info(f"策略权重已更新: {self.strategy_weights}")
    
    def get_system_stats(self) -> Dict:
        """获取系统统计"""
        accuracy = (self.stats['correct_recognitions'] / 
                   max(self.stats['total_recognitions'], 1))
        
        # 收集各策略统计
        strategy_stats = {}
        for strategy, recognizer in self.recognizers.items():
            try:
                stats = recognizer.get_system_stats()
                strategy_stats[strategy] = stats
            except Exception as e:
                logger.error(f"获取策略 {strategy} 统计失败: {e}")
        
        return {
            'registered_cats': len(self.cat_database),
            'total_registrations': self.stats['total_registrations'],
            'total_recognitions': self.stats['total_recognitions'],
            'ensemble_accuracy': accuracy,
            'strategy_weights': self.strategy_weights,
            'strategy_stats': strategy_stats
        }

def test_ensemble_final(dataset_path: str, model_path: str, 
                       scales: List[int] = [5, 10, 20], rounds: int = 3):
    """测试最终集成识别器"""
    dataset_path = Path(dataset_path)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    print("🚀 最终集成识别器测试")
    print("=" * 80)
    print(f"可用猫咪数量: {len(available_cats)}")
    print(f"测试规模: {scales}")
    print(f"每个规模轮数: {rounds}")
    
    results = {}
    
    for scale in scales:
        if scale > len(available_cats):
            print(f"⚠️ 跳过规模 {scale}: 可用数据不足")
            continue
        
        print(f"\n🎯 测试规模: {scale} 只猫咪")
        print("-" * 50)
        
        scale_results = []
        
        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮...")
            
            # 创建集成识别器
            ensemble = EnsembleFinalRecognizer(model_path, device='cpu')
            
            # 随机选择猫咪
            selected_cats = random.sample(available_cats, scale)
            
            # 注册猫咪
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = ensemble.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            similarities = []
            consensus_strengths = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                result = ensemble.recognize_cat(test_image)
                
                total += 1
                is_correct = result.get('success') and result.get('cat_id') == cat_id
                
                if is_correct:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                similarities.append(result.get('similarity', 0.0))
                
                if 'ensemble_info' in result:
                    consensus_strengths.append(result['ensemble_info'].get('consensus_strength', 0.0))
            
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_similarity = np.mean(similarities) if similarities else 0.0
            avg_consensus = np.mean(consensus_strengths) if consensus_strengths else 0.0
            
            scale_results.append({
                'accuracy': accuracy,
                'avg_confidence': avg_confidence,
                'avg_similarity': avg_similarity,
                'avg_consensus': avg_consensus,
                'correct': correct,
                'total': total
            })
            
            print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, "
                  f"相似度: {avg_similarity:.3f}, 共识强度: {avg_consensus:.3f}")
        
        # 计算统计
        accuracies = [r['accuracy'] for r in scale_results]
        avg_accuracy = np.mean(accuracies)
        accuracy_std = np.std(accuracies)
        
        results[scale] = {
            'avg_accuracy': avg_accuracy,
            'accuracy_std': accuracy_std,
            'rounds': scale_results,
            'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
            'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95)
        }
        
        print(f"✅ 平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
        print(f"   高准确率轮次: {results[scale]['high_accuracy_rounds']}/{rounds}")
    
    return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='最终集成识别器测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    
    args = parser.parse_args()
    
    # 运行测试
    results = test_ensemble_final(args.dataset, args.model, args.scales, args.rounds)
    
    # 保存结果
    import json
    with open('ensemble_final_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 最终集成测试结果已保存: ensemble_final_results.json")

if __name__ == "__main__":
    main()
