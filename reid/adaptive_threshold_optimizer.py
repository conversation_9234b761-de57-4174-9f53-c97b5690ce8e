#!/usr/bin/env python3
"""
自适应阈值优化器 - 基于测试结果动态优化识别阈值
"""

import os
import sys
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import json
from pathlib import Path
import random
from collections import defaultdict

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from enhanced_recognizer import create_enhanced_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ThresholdOptimizer:
    """阈值优化器"""
    
    def __init__(self, dataset_path: str, model_path: str):
        self.dataset_path = Path(dataset_path)
        self.model_path = model_path
        
        # 加载数据
        self.available_cats = self._load_cat_data()
        
        logger.info(f"阈值优化器初始化完成: {len(self.available_cats)} 只可用猫咪")
    
    def _load_cat_data(self) -> List[Tuple[str, List[str]]]:
        """加载猫咪数据"""
        cats = []
        for cat_folder in self.dataset_path.iterdir():
            if not cat_folder.is_dir() or not cat_folder.name.isdigit():
                continue
            
            images = []
            for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
                images.extend(list(cat_folder.glob(ext)))
            
            if len(images) >= 5:
                cats.append((cat_folder.name, [str(img) for img in images]))
        
        cats.sort(key=lambda x: len(x[1]), reverse=True)
        return cats
    
    def collect_similarity_data(self, num_cats: int, rounds: int = 5) -> Dict:
        """收集相似度数据用于阈值优化"""
        print(f"🔍 收集 {num_cats} 只猫咪的相似度数据 ({rounds} 轮)")
        
        positive_similarities = []  # 正确匹配的相似度
        negative_similarities = []  # 错误匹配的相似度
        
        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮...")
            
            # 创建识别器
            recognizer = create_enhanced_recognizer(self.model_path, device='cpu')
            
            # 随机选择猫咪
            selected_cats = random.sample(self.available_cats, num_cats)
            
            # 注册猫咪
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 收集相似度数据
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                result = recognizer.recognize_cat(test_image)
                
                if 'top_matches' in result and result['top_matches']:
                    # 找到正确匹配和错误匹配的相似度
                    for match in result['top_matches']:
                        if match['cat_id'] == cat_id:
                            positive_similarities.append(match['similarity'])
                        else:
                            negative_similarities.append(match['similarity'])
        
        return {
            'positive_similarities': positive_similarities,
            'negative_similarities': negative_similarities,
            'num_cats': num_cats,
            'rounds': rounds
        }
    
    def optimize_threshold(self, similarity_data: Dict) -> Dict:
        """基于相似度数据优化阈值"""
        pos_sims = np.array(similarity_data['positive_similarities'])
        neg_sims = np.array(similarity_data['negative_similarities'])
        
        if len(pos_sims) == 0 or len(neg_sims) == 0:
            return {'optimal_threshold': 0.65, 'confidence': 0.0}
        
        # 计算统计信息
        pos_mean = np.mean(pos_sims)
        pos_std = np.std(pos_sims)
        neg_mean = np.mean(neg_sims)
        neg_std = np.std(neg_sims)
        
        print(f"正确匹配相似度: {pos_mean:.3f} ± {pos_std:.3f}")
        print(f"错误匹配相似度: {neg_mean:.3f} ± {neg_std:.3f}")
        
        # 尝试不同阈值，找到最佳平衡点
        thresholds = np.arange(0.3, 0.9, 0.01)
        best_threshold = 0.65
        best_f1 = 0.0
        
        results = []
        
        for threshold in thresholds:
            # 计算准确率、召回率、F1分数
            tp = np.sum(pos_sims >= threshold)  # 正确识别为正例
            fp = np.sum(neg_sims >= threshold)  # 错误识别为正例
            fn = np.sum(pos_sims < threshold)   # 错误识别为负例
            tn = np.sum(neg_sims < threshold)   # 正确识别为负例
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            accuracy = (tp + tn) / (tp + fp + fn + tn) if (tp + fp + fn + tn) > 0 else 0
            
            results.append({
                'threshold': threshold,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'accuracy': accuracy
            })
            
            if f1 > best_f1:
                best_f1 = f1
                best_threshold = threshold
        
        # 找到最佳结果
        best_result = next(r for r in results if r['threshold'] == best_threshold)
        
        return {
            'optimal_threshold': best_threshold,
            'best_f1': best_f1,
            'best_precision': best_result['precision'],
            'best_recall': best_result['recall'],
            'best_accuracy': best_result['accuracy'],
            'pos_mean': pos_mean,
            'pos_std': pos_std,
            'neg_mean': neg_mean,
            'neg_std': neg_std,
            'separation': pos_mean - neg_mean,
            'all_results': results
        }
    
    def optimize_for_scales(self, scales: List[int]) -> Dict:
        """为不同规模优化阈值"""
        print("🎯 多规模阈值优化")
        print("=" * 50)
        
        scale_optimizations = {}
        
        for scale in scales:
            if scale > len(self.available_cats):
                print(f"⚠️ 跳过规模 {scale}: 数据不足")
                continue
            
            print(f"\n📊 优化规模 {scale}")
            print("-" * 30)
            
            # 收集相似度数据
            similarity_data = self.collect_similarity_data(scale, rounds=3)
            
            # 优化阈值
            optimization_result = self.optimize_threshold(similarity_data)
            
            scale_optimizations[scale] = {
                'similarity_data': similarity_data,
                'optimization': optimization_result
            }
            
            print(f"✅ 最佳阈值: {optimization_result['optimal_threshold']:.3f}")
            print(f"   F1分数: {optimization_result['best_f1']:.3f}")
            print(f"   准确率: {optimization_result['best_accuracy']:.3f}")
            print(f"   分离度: {optimization_result['separation']:.3f}")
        
        return scale_optimizations
    
    def generate_adaptive_threshold_function(self, optimizations: Dict) -> str:
        """生成自适应阈值函数代码"""
        # 提取规模和对应的最佳阈值
        scales = sorted(optimizations.keys())
        thresholds = [optimizations[scale]['optimization']['optimal_threshold'] for scale in scales]
        
        # 生成函数代码
        function_code = '''def get_optimized_adaptive_threshold(num_cats: int) -> float:
    """优化后的自适应阈值函数"""
    # 基于实际测试数据优化的阈值
    scale_thresholds = {'''
        
        for scale, threshold in zip(scales, thresholds):
            function_code += f'\n        {scale}: {threshold:.3f},'
        
        function_code += '''
    }
    
    # 如果正好匹配某个规模
    if num_cats in scale_thresholds:
        return scale_thresholds[num_cats]
    
    # 线性插值
    sorted_scales = sorted(scale_thresholds.keys())
    
    if num_cats <= sorted_scales[0]:
        return scale_thresholds[sorted_scales[0]]
    elif num_cats >= sorted_scales[-1]:
        return scale_thresholds[sorted_scales[-1]]
    else:
        # 找到相邻的两个规模进行插值
        for i in range(len(sorted_scales) - 1):
            if sorted_scales[i] <= num_cats <= sorted_scales[i + 1]:
                x1, y1 = sorted_scales[i], scale_thresholds[sorted_scales[i]]
                x2, y2 = sorted_scales[i + 1], scale_thresholds[sorted_scales[i + 1]]
                
                # 线性插值
                return y1 + (y2 - y1) * (num_cats - x1) / (x2 - x1)
    
    # 默认值
    return 0.65'''
        
        return function_code

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='阈值优化')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10, 20],
                       help='优化的规模列表')
    parser.add_argument('--output', type=str, default='threshold_optimization.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 创建优化器
    optimizer = ThresholdOptimizer(args.dataset, args.model)
    
    # 运行优化
    optimizations = optimizer.optimize_for_scales(args.scales)
    
    # 生成自适应函数
    function_code = optimizer.generate_adaptive_threshold_function(optimizations)
    
    # 保存结果
    results = {
        'optimizations': optimizations,
        'adaptive_function_code': function_code,
        'summary': {
            'scales': list(optimizations.keys()),
            'optimal_thresholds': {
                scale: opt['optimization']['optimal_threshold'] 
                for scale, opt in optimizations.items()
            },
            'f1_scores': {
                scale: opt['optimization']['best_f1'] 
                for scale, opt in optimizations.items()
            }
        }
    }
    
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # 保存函数代码
    with open('optimized_threshold_function.py', 'w') as f:
        f.write(function_code)
    
    print(f"\n💾 优化结果已保存: {args.output}")
    print(f"📝 函数代码已保存: optimized_threshold_function.py")
    
    # 显示总结
    print(f"\n📊 优化总结:")
    for scale in sorted(optimizations.keys()):
        opt = optimizations[scale]['optimization']
        print(f"   {scale}只猫咪: 阈值={opt['optimal_threshold']:.3f}, F1={opt['best_f1']:.3f}")

if __name__ == "__main__":
    main()
