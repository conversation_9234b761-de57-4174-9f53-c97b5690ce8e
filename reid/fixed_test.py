#!/usr/bin/env python3
"""
修正版测试脚本 - 使用正确的MegaDescriptor模型
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from fixed_universal_recognizer import create_fixed_universal_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_fixed_test(dataset_path: str, cat_counts: List[int] = [3, 5, 10], rounds: int = 3):
    """运行修正版测试"""
    dataset_path = Path(dataset_path)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    print("🚀 修正版通用猫咪识别系统测试")
    print("=" * 60)
    print(f"可用猫咪数量: {len(available_cats)}")
    
    results = {}
    
    for cat_count in cat_counts:
        if cat_count > len(available_cats):
            print(f"⚠️ 跳过 {cat_count} 只猫咪测试：可用数据不足")
            continue
        
        print(f"\n🎯 测试 {cat_count} 只猫咪")
        print("-" * 40)
        
        round_results = []
        
        for round_num in range(rounds):
            print(f"第 {round_num + 1} 轮测试...")
            
            # 创建识别器
            recognizer = create_fixed_universal_recognizer()
            
            # 随机选择猫咪
            selected_cats = random.sample(available_cats, cat_count)
            
            # 注册猫咪
            registered_cats = []
            for cat_id, image_paths in selected_cats:
                train_count = max(3, int(len(image_paths) * 0.7))
                train_images = image_paths[:train_count]
                test_images = image_paths[train_count:]
                
                result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                if result['success'] and test_images:
                    registered_cats.append((cat_id, test_images))
            
            # 识别测试
            correct = 0
            total = 0
            confidences = []
            response_times = []
            
            for cat_id, test_images in registered_cats:
                test_image = random.choice(test_images)
                
                start_time = time.time()
                result = recognizer.recognize_cat(test_image)
                response_time = time.time() - start_time
                
                response_times.append(response_time)
                total += 1
                
                if result.get('success') and result.get('cat_id') == cat_id:
                    correct += 1
                    confidences.append(result.get('confidence', 0.0))
                
                # 显示详细结果
                status = "✅" if (result.get('success') and result.get('cat_id') == cat_id) else "❌"
                confidence = result.get('confidence', 0.0)
                print(f"    {status} {cat_id}: {confidence:.1%} ({response_time:.3f}s)")
            
            accuracy = correct / total if total > 0 else 0.0
            avg_confidence = np.mean(confidences) if confidences else 0.0
            avg_response_time = np.mean(response_times) if response_times else 0.0
            
            round_results.append({
                'accuracy': accuracy,
                'confidence': avg_confidence,
                'response_time': avg_response_time,
                'correct': correct,
                'total': total
            })
            
            print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}")
        
        # 计算统计
        accuracies = [r['accuracy'] for r in round_results]
        avg_accuracy = np.mean(accuracies)
        accuracy_std = np.std(accuracies)
        
        results[cat_count] = {
            'avg_accuracy': avg_accuracy,
            'accuracy_std': accuracy_std,
            'rounds': round_results,
            'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
            'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95)
        }
        
        print(f"✅ 平均准确率: {avg_accuracy:.1%} ± {accuracy_std:.1%}")
        print(f"   高准确率轮次: {results[cat_count]['high_accuracy_rounds']}/{rounds}")
    
    return results

def display_summary(results: Dict):
    """显示测试总结"""
    print("\n" + "=" * 60)
    print("📊 修正版测试总结")
    print("=" * 60)
    
    if not results:
        print("没有测试结果")
        return
    
    print(f"{'规模':<8} {'平均准确率':<12} {'稳定性':<10} {'高准确率轮次':<12}")
    print("-" * 50)
    
    for scale in sorted(results.keys()):
        result = results[scale]
        stability = 1.0 - result['accuracy_std']
        high_acc_ratio = result['high_accuracy_rounds'] / len(result['rounds'])
        
        print(f"{scale:<8} {result['avg_accuracy']:<12.1%} {stability:<10.1%} {high_acc_ratio:<12.1%}")
    
    # 系统评价
    min_accuracy = min(results[scale]['avg_accuracy'] for scale in results)
    max_scale = max(results.keys())
    
    print(f"\n🎯 系统评价:")
    print(f"   最大测试规模: {max_scale} 只猫咪")
    print(f"   最低准确率: {min_accuracy:.1%}")
    
    if min_accuracy >= 0.95:
        rating = "🌟 优秀 - 支持任意规模高精度识别"
    elif min_accuracy >= 0.9:
        rating = "✅ 良好 - 具备良好的规模扩展性"
    elif min_accuracy >= 0.8:
        rating = "⚠️ 一般 - 大规模场景需要优化"
    else:
        rating = "❌ 需要改进 - 规模扩展性不足"
    
    print(f"   总体评价: {rating}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if min_accuracy < 0.9:
        print(f"   • 考虑增加更多训练数据")
        print(f"   • 调整相似度阈值")
        print(f"   • 改进特征提取算法")
    else:
        print(f"   • 系统表现良好，可考虑扩展到更大规模")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='修正版识别系统测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=3,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='fixed_test_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 运行测试
    results = run_fixed_test(args.dataset, args.scales, args.rounds)
    
    # 显示结果
    display_summary(results)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
