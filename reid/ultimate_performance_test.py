#!/usr/bin/env python3
"""
终极性能测试 - 验证所有优化策略的最终效果
"""

import os
import sys
import time
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
import logging
import argparse
import json

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from adaptive_recognizer import create_adaptive_recognizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_ultimate_test(dataset_path: str, model_path: str, 
                     scales: List[int] = [3, 5, 10, 20, 30], rounds: int = 5):
    """运行终极性能测试"""
    dataset_path = Path(dataset_path)
    
    # 获取可用猫咪
    available_cats = []
    for cat_folder in dataset_path.iterdir():
        if not cat_folder.is_dir() or not cat_folder.name.isdigit():
            continue
        
        images = []
        for ext in ['*.jpg', '*.JPG', '*.jpeg', '*.JPEG', '*.png', '*.PNG']:
            images.extend(list(cat_folder.glob(ext)))
        
        if len(images) >= 5:
            available_cats.append((cat_folder.name, [str(img) for img in images]))
    
    available_cats.sort(key=lambda x: len(x[1]), reverse=True)
    
    print("🚀 终极性能测试")
    print("=" * 80)
    print(f"可用猫咪数量: {len(available_cats)}")
    print(f"测试规模: {scales}")
    print(f"每个规模轮数: {rounds}")
    print(f"使用模型: {'增强模型' if model_path else '基础模型'}")
    
    results = {}
    strategies = ['conservative', 'balanced', 'aggressive']
    
    for scale in scales:
        if scale > len(available_cats):
            print(f"⚠️ 跳过规模 {scale}: 可用数据不足")
            continue
        
        print(f"\n🎯 测试规模: {scale} 只猫咪")
        print("=" * 60)
        
        scale_results = {}
        
        for strategy in strategies:
            print(f"\n📊 策略: {strategy}")
            print("-" * 30)
            
            strategy_rounds = []
            
            for round_num in range(rounds):
                print(f"第 {round_num + 1} 轮...")
                
                # 创建自适应识别器
                recognizer = create_adaptive_recognizer(model_path, device='cpu', strategy=strategy)
                
                # 随机选择猫咪
                selected_cats = random.sample(available_cats, scale)
                
                # 注册和测试
                registered_cats = []
                test_data = []
                
                for cat_id, image_paths in selected_cats:
                    train_count = max(3, int(len(image_paths) * 0.7))
                    train_images = image_paths[:train_count]
                    test_images = image_paths[train_count:]
                    
                    result = recognizer.register_cat(cat_id, f"Cat_{cat_id}", train_images)
                    if result['success'] and test_images:
                        registered_cats.append((cat_id, test_images))
                        test_data.append((cat_id, random.choice(test_images)))
                
                # 识别测试
                correct = 0
                total = 0
                confidences = []
                similarities = []
                response_times = []
                
                for cat_id, test_image in test_data:
                    start_time = time.time()
                    result = recognizer.recognize_cat(test_image)
                    response_time = time.time() - start_time
                    
                    response_times.append(response_time)
                    total += 1
                    
                    is_correct = result.get('success') and result.get('cat_id') == cat_id
                    if is_correct:
                        correct += 1
                        confidences.append(result.get('confidence', 0.0))
                    
                    similarities.append(result.get('similarity', 0.0))
                
                accuracy = correct / total if total > 0 else 0.0
                avg_confidence = np.mean(confidences) if confidences else 0.0
                avg_similarity = np.mean(similarities) if similarities else 0.0
                avg_response_time = np.mean(response_times) if response_times else 0.0
                
                strategy_rounds.append({
                    'accuracy': accuracy,
                    'avg_confidence': avg_confidence,
                    'avg_similarity': avg_similarity,
                    'avg_response_time': avg_response_time,
                    'correct': correct,
                    'total': total
                })
                
                print(f"  准确率: {accuracy:.1%}, 置信度: {avg_confidence:.1%}, "
                      f"相似度: {avg_similarity:.3f}, 响应时间: {avg_response_time:.3f}s")
            
            # 计算策略统计
            accuracies = [r['accuracy'] for r in strategy_rounds]
            avg_accuracy = np.mean(accuracies)
            accuracy_std = np.std(accuracies)
            
            scale_results[strategy] = {
                'avg_accuracy': avg_accuracy,
                'accuracy_std': accuracy_std,
                'min_accuracy': np.min(accuracies),
                'max_accuracy': np.max(accuracies),
                'avg_confidence': np.mean([r['avg_confidence'] for r in strategy_rounds]),
                'avg_similarity': np.mean([r['avg_similarity'] for r in strategy_rounds]),
                'avg_response_time': np.mean([r['avg_response_time'] for r in strategy_rounds]),
                'rounds': strategy_rounds,
                'perfect_rounds': sum(1 for acc in accuracies if acc == 1.0),
                'high_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.95),
                'good_accuracy_rounds': sum(1 for acc in accuracies if acc >= 0.9)
            }
            
            print(f"✅ {strategy}: 平均准确率 {avg_accuracy:.1%} ± {accuracy_std:.1%}")
            print(f"   完美轮次: {scale_results[strategy]['perfect_rounds']}/{rounds}")
            print(f"   高准确率轮次(95%+): {scale_results[strategy]['high_accuracy_rounds']}/{rounds}")
            print(f"   良好准确率轮次(90%+): {scale_results[strategy]['good_accuracy_rounds']}/{rounds}")
        
        results[scale] = scale_results
        
        # 显示规模总结
        best_strategy = max(scale_results.keys(), key=lambda s: scale_results[s]['avg_accuracy'])
        best_accuracy = scale_results[best_strategy]['avg_accuracy']
        
        print(f"\n🏆 规模 {scale} 最佳策略: {best_strategy} (准确率: {best_accuracy:.1%})")
    
    return results

def display_ultimate_summary(results: Dict):
    """显示终极测试总结"""
    print("\n" + "=" * 100)
    print("🎯 终极性能测试总结")
    print("=" * 100)
    
    if not results:
        print("没有测试结果")
        return
    
    # 总体表现表格
    print(f"{'规模':<6} {'保守策略':<12} {'平衡策略':<12} {'激进策略':<12} {'最佳策略':<10} {'最佳准确率':<12}")
    print("-" * 80)
    
    overall_best_accuracy = 0
    overall_best_scale = 0
    overall_best_strategy = ""
    target_95_achieved = 0
    target_90_achieved = 0
    
    for scale in sorted(results.keys()):
        scale_result = results[scale]
        
        conservative_acc = scale_result['conservative']['avg_accuracy']
        balanced_acc = scale_result['balanced']['avg_accuracy']
        aggressive_acc = scale_result['aggressive']['avg_accuracy']
        
        best_strategy = max(scale_result.keys(), key=lambda s: scale_result[s]['avg_accuracy'])
        best_accuracy = scale_result[best_strategy]['avg_accuracy']
        
        print(f"{scale:<6} {conservative_acc:<12.1%} {balanced_acc:<12.1%} "
              f"{aggressive_acc:<12.1%} {best_strategy:<10} {best_accuracy:<12.1%}")
        
        # 更新总体最佳
        if best_accuracy > overall_best_accuracy:
            overall_best_accuracy = best_accuracy
            overall_best_scale = scale
            overall_best_strategy = best_strategy
        
        # 统计目标达成
        if best_accuracy >= 0.95:
            target_95_achieved += 1
        if best_accuracy >= 0.90:
            target_90_achieved += 1
    
    # 总体评价
    print(f"\n🏆 总体最佳表现:")
    print(f"   规模: {overall_best_scale} 只猫咪")
    print(f"   策略: {overall_best_strategy}")
    print(f"   准确率: {overall_best_accuracy:.1%}")
    
    # 目标达成情况
    total_scales = len(results)
    print(f"\n🎯 目标达成情况:")
    print(f"   达到95%+准确率的规模: {target_95_achieved}/{total_scales} ({target_95_achieved/total_scales:.1%})")
    print(f"   达到90%+准确率的规模: {target_90_achieved}/{total_scales} ({target_90_achieved/total_scales:.1%})")
    
    # 最终评级
    if target_95_achieved == total_scales:
        rating = "🌟 完美 - 全部达成95%+目标!"
        grade = "A+"
    elif target_95_achieved >= total_scales * 0.8:
        rating = "🎉 优秀 - 大部分达成95%+目标"
        grade = "A"
    elif target_90_achieved == total_scales:
        rating = "✅ 良好 - 全部达成90%+目标"
        grade = "B+"
    elif target_90_achieved >= total_scales * 0.8:
        rating = "📈 不错 - 大部分达成90%+目标"
        grade = "B"
    else:
        rating = "⚠️ 需要改进 - 距离目标还有差距"
        grade = "C"
    
    print(f"\n📊 最终评级: {grade}")
    print(f"   评价: {rating}")
    
    # 策略分析
    print(f"\n🔍 策略分析:")
    strategy_performance = {'conservative': [], 'balanced': [], 'aggressive': []}
    
    for scale in results:
        for strategy in strategy_performance:
            strategy_performance[strategy].append(results[scale][strategy]['avg_accuracy'])
    
    for strategy, accuracies in strategy_performance.items():
        avg_acc = np.mean(accuracies)
        std_acc = np.std(accuracies)
        print(f"   {strategy:>12}: 平均 {avg_acc:.1%} ± {std_acc:.1%}")
    
    best_overall_strategy = max(strategy_performance.keys(), 
                               key=lambda s: np.mean(strategy_performance[s]))
    print(f"   🏆 最佳总体策略: {best_overall_strategy}")
    
    # 改进建议
    print(f"\n💡 改进建议:")
    if overall_best_accuracy >= 0.95:
        print(f"   🎉 系统表现优秀，已达到目标!")
        print(f"   • 可以考虑部署到生产环境")
        print(f"   • 建议进行更大规模的测试验证")
    elif overall_best_accuracy >= 0.90:
        print(f"   📈 系统表现良好，接近目标")
        print(f"   • 建议微调阈值参数")
        print(f"   • 可以尝试集成学习方法")
    else:
        print(f"   ⚠️ 系统需要进一步优化")
        print(f"   • 建议增加训练数据")
        print(f"   • 考虑改进模型架构")
        print(f"   • 优化特征提取算法")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='终极性能测试')
    parser.add_argument('--dataset', type=str, 
                       default='/home/<USER>/animsi/caby_training/dataset/cat_individual_images',
                       help='数据集路径')
    parser.add_argument('--model', type=str, 
                       default='training/enhanced_megadescriptor_50cats.pth',
                       help='模型路径')
    parser.add_argument('--scales', type=int, nargs='+', default=[3, 5, 10, 20],
                       help='测试规模列表')
    parser.add_argument('--rounds', type=int, default=5,
                       help='每个规模的测试轮数')
    parser.add_argument('--output', type=str, default='ultimate_test_results.json',
                       help='结果保存路径')
    
    args = parser.parse_args()
    
    # 运行终极测试
    results = run_ultimate_test(args.dataset, args.model, args.scales, args.rounds)
    
    # 显示总结
    display_ultimate_summary(results)
    
    # 保存结果
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 终极测试结果已保存: {args.output}")

if __name__ == "__main__":
    main()
